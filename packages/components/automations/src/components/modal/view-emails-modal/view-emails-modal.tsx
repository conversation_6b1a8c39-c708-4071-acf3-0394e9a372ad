import React, {
	useCallback,
	useEffect,
	useMemo,
	useState,
	useRef,
	SetStateAction,
} from 'react';
import { useTranslation } from '@activecampaign/core-translations-client';
import Modal from '@activecampaign/camp-components-modal';
import {
	useMutation,
	useQueryClient,
} from '@activecampaign/platform-core-queries';
import { DefaultSection, EditSenderSection, TestSection } from './sections';
import useGetAutomationCampaignMessages from '../../../hooks/useGetAutomationCampaignMessages';
import useDeleteCampaign from '../../../hooks/useDeleteCampaign';
import useSendTestEmails from '../../../hooks/useSendTestEmails';
import useGetLists from '../../../hooks/useGetLists';
import useCreateSeriesList from '../../../hooks/useCreateSeriesList';
import useGetSeriesLists from '../../../hooks/useGetSeriesLists';
import useGetAutomationBlocks from '../../../hooks/useGetAutomationBlocks';
import useConfigListDropdownHover from '../../../hooks/useConfigListDropdownHover';
import Banner from '@activecampaign/camp-components-banner';
import { EnhancedCampaignMessage } from '@activecampaign/platform-core-queries/src';
import { useGlobalData } from '@activecampaign/platform-core-queries';
import DeleteCampaignSection from './sections/delete-campaign';
import { EditSenderForm } from './sections/edit-sender-section/edit-sender-section';
import axios from 'axios';
import {
	ToastDuration,
	createToast,
} from '@activecampaign/camp-components-toast';
import { hasListPermission } from './utils';
import { OptionObject } from '@activecampaign/camp-components-dropdown';

export type VisibleEmailsModalSections =
	| 'default'
	| 'delete-campaign'
	| 'test-emails'
	| 'edit-sender';

type ViewEmailsModalProps = {
	automationId: number;
	onDelete: (campaignId: string, blockId: string) => void;
	lastFetchedTimestamp?: number; // Adding timestamp to tell react-query to refetch data after deleting a campaign from the outside of react app
	isOpen: boolean;
	setIsOpen: React.Dispatch<SetStateAction<boolean>>;
};

type EditSenderUpdateValues = {
	id: string;
	fromname: string;
	fromemail: string;
	reply2: string;
};

type ListDropdownOption = {
	id: number;
	name: string;
	origin?: number;
	disabled?: boolean;
	render?: (option: OptionObject) => React.ReactElement;
};

type CreateSeriesListBody = {
	listid: number;
	origin: number;
};

const numberOfListsRetrivedPerCall = 100;

const origin = Object.freeze({
	byTrigger: 0,
	byUser: 1,
	default: 2,
});

const ViewEmailsModal: React.FC<ViewEmailsModalProps> = ({
	automationId,
	lastFetchedTimestamp,
	onDelete,
	isOpen,
	setIsOpen,
}) => {
	//Declaration of Translation
	const { t } = useTranslation();

	//Declaration of Get Hooks
	const {
		data,
		isError,
		isFetching: isFetchingCampaigns,
		refetch: refetchCampaignMessages,
	} = useGetAutomationCampaignMessages(automationId);

	const {
		data: blocks,
		isFetching: isFetchingBlocks,
		refetch: refreshAutomationBlocks,
	} = useGetAutomationBlocks(automationId);

	const { data: listsData, isFetching: isFetchingLists } = useGetLists(
		numberOfListsRetrivedPerCall
	);

	const {
		data: seriesListData,
		isFetching: isFetchingSeriesList,
		refetch: refreshListAssociations,
	} = useGetSeriesLists(automationId);

	const { configDropdownObject } = useConfigListDropdownHover(blocks);

	const queryClient = useQueryClient();

	const { data: globalData } = useGlobalData();

	const { admin } = globalData ?? { admin: {} };
	const { userJson } = globalData ?? { userJson: {} };
	const userHasListPermission = hasListPermission(userJson);

	//Declaration of the state variables
	const [selectedEmails, setSelectedEmails] = useState<
		EnhancedCampaignMessage[]
	>([]);

	const [campaignToDelete, setCampaignToDelete] = useState<number>();

	const [visibleSection, setVisibleSection] =
		useState<VisibleEmailsModalSections>('default');

	const [selectedLists, setSelectedLists] = useState<ListDropdownOption[]>([]);

	const automationCurrentLists = useRef(null);

	//Declaration of mutations
	const { mutateAsync: deleteCampaign, isLoading: isDeleteCampaignLoading } =
		useDeleteCampaign();

	const { mutateAsync: sendTestEmails, isLoading: isSendTestEmailsLoading } =
		useSendTestEmails();

	const { mutateAsync: createSeriesList } = useCreateSeriesList();

	//Function related to change the modal state and content
	const handleModalToggle = useCallback((): void => {
		setIsOpen((current) => !current);
		setVisibleSection('default');
		setSelectedEmails([]);
	}, [setIsOpen]);

	/**
	 * This function uses the current state of the selected lists and the lists that came from the API.
	 * @returns true if the arrays are equal, false if they are not. So the API is not called if the arrays are equal.
	 */

	const areArraysEqual = useCallback(() => {
		// Create copies of the arrays to avoid modifying the original arrays
		const apiSeriesList = automationCurrentLists.current;
		const dropdownSeleected = [...selectedLists];
		// Check if the arrays have the same length
		if (apiSeriesList.length !== dropdownSeleected.length) {
			return false;
		}

		const idSet1 = new Set(apiSeriesList.map((obj) => obj.id));
		const idSet2 = new Set(dropdownSeleected.map((obj) => obj.id));

		// Check if the sets of ids are equal
		return (
			apiSeriesList.every((obj) => idSet2.has(obj.id)) &&
			dropdownSeleected.every((obj) => idSet1.has(obj.id))
		);
	}, [automationCurrentLists, selectedLists]);

	const handleDoneClick = useCallback(() => {
		const listsToSave: CreateSeriesListBody[] = selectedLists.map(
			(listOption) => ({
				listid: listOption.id,
				origin: origin.byUser,
			})
		);

		if (listsToSave && !areArraysEqual()) {
			createSeriesList({
				seriesid: automationId,
				lists: listsToSave,
			});
		}
		handleModalToggle();
	}, [
		automationId,
		handleModalToggle,
		createSeriesList,
		selectedLists,
		areArraysEqual,
	]);

	const handleModalContentChange = (
		section: VisibleEmailsModalSections
	): void => {
		setVisibleSection(section);
	};

	const handleTestAllEmailsClick = useCallback((): void => {
		setSelectedEmails(data?.campaignMessages);
		setVisibleSection('test-emails');
	}, [data?.campaignMessages]);

	const handleEditAllSendersClick = useCallback((): void => {
		setSelectedEmails(data?.campaignMessages);
		setVisibleSection('edit-sender');
	}, [data?.campaignMessages]);

	//Function related to the dropdown list in Default Section
	const handleListDropdownChange = useCallback(
		(newSelectedOptions): void => {
			setSelectedLists(newSelectedOptions);
		},
		[setSelectedLists]
	);

	// Constant that have all the options of the dropdown list
	const allOptions = useMemo(() => {
		const allOptions = listsData
			? listsData?.map((list) => {
					const listOriginObject = seriesListData?.find(
						(selectedList) => selectedList.listId === list.id
					);
					return {
						id: list.id,
						name: list.name,
						origin: listOriginObject ? listOriginObject.origin : 1,
					};
			  })
			: [];

		// Sort options first, as the config checkbox needs to be always first.
		const sortedOptions = allOptions.sort((list1, list2) =>
			list1.name.localeCompare(list2.name)
		);

		return sortedOptions;
	}, [listsData, seriesListData]);

	//Function related to the delete modal
	const getBlockIdFromCampaign = useCallback(
		(campaignId: number): number => {
			return blocks.find(({ vars }) => {
				return vars.campaignid === campaignId;
			})?.id;
		},
		[blocks]
	);

	const onDeleteCampaign = useCallback(async (): Promise<void> => {
		deleteCampaign(campaignToDelete).then((response: Response) => {
			if (response.status === 200) {
				const blockId = getBlockIdFromCampaign(campaignToDelete);
				createToast({
					appearance: 'success',
					title: t('automations:view-emails:delete-section:success'),
					titleTestId: 'delete-section-toast-success',
					duration: ToastDuration.Standard,
				});
				setCampaignToDelete(undefined);
				setVisibleSection('default');
				onDelete(campaignToDelete.toString(), blockId.toString());
				setVisibleSection('default');
				handleModalToggle();
			} else {
				createToast({
					appearance: 'danger',
					title: t('automations:view-emails:delete-section:error'),
					titleTestId: 'delete-section-toast-error',
					duration: ToastDuration.Standard,
				});
			}
		});
	}, [
		campaignToDelete,
		deleteCampaign,
		t,
		handleModalToggle,
		getBlockIdFromCampaign,
		onDelete,
	]);

	//Funcion related to edit sender modal
	//This mutation is here because it needs the handleModalToggle to be declared
	const { mutate: mutateEditSender, isLoading: isEditSenderLoading } =
		useMutation({
			mutationFn: (emailsToSend: EditSenderUpdateValues[]) => {
				return Promise.all(
					emailsToSend.map((email) =>
						axios
							.put(`/api/3/messages/${email.id}`, {
								message: {
									fromname: email.fromname,
									fromemail: email.fromemail,
									reply2: email.reply2,
								},
							})
							.then((value) => ({
								status: 'fulfilled',
								value,
							}))
							.catch((value) => ({
								status: 'rejected',
								value,
							}))
					)
				);
			},
			onSuccess: (promisesResults) => {
				queryClient.invalidateQueries({
					queryKey: ['automationId', automationId],
				});

				const promisesStatuses = promisesResults.map(
					(messagePromise) => messagePromise.status
				);
				if (promisesStatuses.includes('fulfilled')) {
					createToast({
						appearance: 'success',
						title: t('automations:view-emails:edit-sender-success'),
						titleTestId: 'edit-sender-toast-success',
						duration: ToastDuration.Standard,
					});
				} else {
					createToast({
						appearance: 'danger',
						title: t('automations:view-emails:edit-sender-error'),
						titleTestId: 'edit-sender-toast-error',
						duration: ToastDuration.Standard,
					});
				}

				handleModalToggle();
			},
		});

	const onEditSenderSaveClick = useCallback(
		(formValues: EditSenderForm): void => {
			const emailDataToUpdate = selectedEmails.map((email) => {
				return {
					id: email.messageid,
					fromname: formValues.fromName,
					fromemail: formValues.fromEmail,
					reply2: formValues.replyTo,
				};
			});
			mutateEditSender(emailDataToUpdate);
		},
		[mutateEditSender, selectedEmails]
	);

	//Function related to the test email modal
	const handleTestSectionSendClick = useCallback(
		async (emailStringList: string): Promise<void> => {
			const emailList = emailStringList.split(',').map((email) => email.trim());
			await Promise.all(
				emailList.flatMap((testEmail) =>
					selectedEmails
						.filter(({ campaignid }) => typeof campaignid !== 'undefined')
						.map(({ fromname, fromemail, subject, campaignid, messageid }) =>
							sendTestEmails({
								testEmail,
								fromName: fromname,
								fromEmail: fromemail,
								subject,
								campaignId: campaignid,
								messageId: messageid,
							})
								.then((value) => ({
									status: 'fulfilled',
									value,
								}))
								.catch((value) => ({
									status: 'rejected',
									value,
								}))
						)
				)
			).then((results) => {
				const promisesStatuses = results.map(
					(messagePromise) => messagePromise.status
				);
				if (promisesStatuses.includes('fulfilled')) {
					createToast({
						appearance: 'success',
						title: t('automations:view-emails:test-section:success'),
						titleTestId: 'test-section-toast-success',
						duration: ToastDuration.Standard,
					});
				} else {
					createToast({
						appearance: 'danger',
						title: t('automations:view-emails:test-section:error'),
						titleTestId: 'test-section-toast-error',
						duration: ToastDuration.Standard,
					});
				}
			});
			handleModalToggle();
		},
		[selectedEmails, handleModalToggle, sendTestEmails, t]
	);

	//Sections of the modal
	const MODAL_SECTIONS = useMemo(
		() => ({
			default: {
				title: t('automations:view-emails:view-emails-modal-title'),
				width: '70%',
				content: (
					<DefaultSection
						data={data}
						loading={isFetchingCampaigns || isFetchingLists || isFetchingBlocks}
						onDoneClick={handleDoneClick}
						handleModalContentChange={handleModalContentChange}
						setSelectedEmails={setSelectedEmails}
						selectedEmails={selectedEmails}
						onTestAllEmailsClick={handleTestAllEmailsClick}
						onEditAllSendersClick={handleEditAllSendersClick}
						blocks={blocks}
						onCampaignDeleteClick={setCampaignToDelete}
						onListDropdownChange={handleListDropdownChange}
						selectedLists={
							configDropdownObject
								? [configDropdownObject, ...selectedLists]
								: selectedLists
						}
						listOptions={
							configDropdownObject
								? [configDropdownObject, ...allOptions]
								: allOptions
						}
						listPermission={userHasListPermission}
					/>
				),
			},
			'test-emails': {
				title: t(
					selectedEmails.length > 1
						? 'automations:view-emails:view-emails-modal-test-title-plural'
						: 'automations:view-emails:view-emails-modal-test-title-sing',
					{
						emailAmount: selectedEmails.length,
					}
				),
				width: '480px',
				content: (
					<TestSection
						onBackClick={(): void => {
							setVisibleSection('default');
						}}
						onSendClick={handleTestSectionSendClick}
						isLoading={isSendTestEmailsLoading}
						initialEmailList={admin?.email}
					/>
				),
			},
			'edit-sender': {
				title: t(
					selectedEmails.length === 1
						? 'automations:view-emails:edit-sender-modal-title-sing'
						: 'automations:view-emails:edit-sender-modal-title-plural',
					{
						emailCount: selectedEmails.length,
					}
				),
				width: '480px',
				content: (
					<EditSenderSection
						onSaveClick={onEditSenderSaveClick}
						onBackClick={(): void => setVisibleSection('default')}
						defaultFormValues={{
							fromEmail:
								selectedEmails.length === 1
									? selectedEmails[0].fromemail
									: admin?.email,
							replyTo:
								selectedEmails.length === 1
									? selectedEmails[0].reply2
									: admin?.email,
							fromName:
								selectedEmails.length === 1
									? selectedEmails[0].fromname
									: admin?.fullname ?? '',
							fromNameHasPers: false,
							fromEmailHasPers: false,
						}}
						isEditSenderLoading={isEditSenderLoading}
					/>
				),
			},
			'delete-campaign': {
				title: t(
					'automations:automations:are-you-sure-you-want-to-delete-this-email'
				),
				width: '480px',
				content: (
					<DeleteCampaignSection
						onDeleteClick={onDeleteCampaign}
						onBackClick={(): void => setVisibleSection('default')}
						isLoading={isDeleteCampaignLoading}
					/>
				),
			},
		}),
		[
			t,
			allOptions,
			selectedEmails,
			data,
			configDropdownObject,
			admin?.email,
			admin?.fullname,
			handleTestAllEmailsClick,
			handleEditAllSendersClick,
			handleTestSectionSendClick,
			onEditSenderSaveClick,
			onDeleteCampaign,
			isFetchingCampaigns,
			isEditSenderLoading,
			isDeleteCampaignLoading,
			isSendTestEmailsLoading,
			blocks,
			selectedLists,
			isFetchingLists,
			handleListDropdownChange,
			handleDoneClick,
			isFetchingBlocks,
			userHasListPermission,
		]
	);

	const { title, content, width } = MODAL_SECTIONS[visibleSection];

	//Use Effects
	useEffect(() => {
		if (isOpen || lastFetchedTimestamp) {
			refetchCampaignMessages();
			refreshAutomationBlocks();
			refreshListAssociations();
		}
	}, [
		isOpen,
		refetchCampaignMessages,
		lastFetchedTimestamp,
		refreshAutomationBlocks,
		refreshListAssociations,
	]);

	useEffect(() => {
		if (
			listsData &&
			seriesListData &&
			!isFetchingLists &&
			!isFetchingSeriesList
		) {
			const selectedOptions = seriesListData
				.map((selected) => {
					const result = allOptions.find(
						(option) => option.id === selected.listId
					);
					return (
						result ?? {
							id: selected.listId,
							origin: selected.origin,
							name: null,
						}
					);
				})
				.filter((option) => option.origin != 0);

			setSelectedLists(selectedOptions);
			automationCurrentLists.current = [...selectedOptions];
		}
	}, [
		setSelectedLists,
		listsData,
		seriesListData,
		isFetchingLists,
		isFetchingSeriesList,
		allOptions,
	]);

	if (isError) {
		return (
			<Banner
				data-testid="manage-emails-error-banner"
				title={t('campaigns:list-error:message')}
				appearance="danger"
			/>
		);
	}

	return (
		<>
			{isOpen && (
				<Modal
					data-testid="view-emails-modal"
					onDismiss={handleModalToggle}
					title={title}
					fluid
					width={width}
					styles={{ position: 'relative' }}
				>
					{content}
				</Modal>
			)}
		</>
	);
};

export default ViewEmailsModal;
