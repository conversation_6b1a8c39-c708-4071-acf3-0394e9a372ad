import { renderHook } from '@testing-library/react-hooks';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { useGetContacts, searchContacts } from '../useGetContacts';
import { ContactsData } from '../../types/add-contacts.types';

jest.mock('@tanstack/react-query', () => ({
	useQuery: jest.fn(),
}));

jest.mock('axios');

const mockContactsData: ContactsData = {
	contacts: [
		{
			id: '1',
			firstName: '<PERSON>',
			lastName: 'Doe',
			email: '<EMAIL>',
			phone: '************',
			organization: 'Test Org',
			cdate: '2023-01-01',
			links: {},
		},
		{
			id: '2',
			firstName: 'Jane',
			lastName: 'Smith',
			email: '<EMAIL>',
			phone: '************',
			organization: 'Test Org 2',
			cdate: '2023-01-02',
			links: {},
		},
	],
	scoreValues: [
		{
			id: '1',
			score: '85',
			contact: '1',
			scoreValue: '85',
		},
	],
	meta: {
		total: '2',
	},
};

describe('useGetContacts', () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('should return loading state initially', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: undefined,
			isLoading: true,
			isError: false,
			error: null,
		});

		const { result } = renderHook(() => useGetContacts('test search'));

		expect(result.current.isLoading).toBe(true);
		expect(result.current.isError).toBe(false);
		expect(result.current.data).toBeUndefined();
	});

	it('should return error state when query fails', () => {
		const mockError = new Error('Network Error');
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: undefined,
			isLoading: false,
			isError: true,
			error: mockError,
		});

		const { result } = renderHook(() => useGetContacts('test search'));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(true);
		expect(result.current.error).toEqual(mockError);
		expect(result.current.data).toBeUndefined();
	});

	it('should return contacts data when query succeeds', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: mockContactsData,
			isLoading: false,
			isError: false,
			error: null,
		});

		const { result } = renderHook(() => useGetContacts('test search'));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(false);
		expect(result.current.data).toEqual(mockContactsData);
		expect(result.current.data?.contacts).toHaveLength(2);
		expect(result.current.data?.meta.total).toBe('2');
	});

	it('should be disabled when search is empty', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: undefined,
			isLoading: false,
			isError: false,
			error: null,
		});

		renderHook(() => useGetContacts(''));

		expect(useQuery).toHaveBeenCalledWith(['contacts+'], expect.any(Function), {
			enabled: false,
		});
	});

	it('should be enabled when search has value', () => {
		const searchTerm = 'john';
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: mockContactsData,
			isLoading: false,
			isError: false,
			error: null,
		});

		renderHook(() => useGetContacts(searchTerm));

		expect(useQuery).toHaveBeenCalledWith(
			[`contacts+${searchTerm}`],
			expect.any(Function),
			{ enabled: true }
		);
	});

	it('should generate correct query key based on search term', () => {
		const searchTerm = 'jane doe';
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: mockContactsData,
			isLoading: false,
			isError: false,
			error: null,
		});

		renderHook(() => useGetContacts(searchTerm));

		expect(useQuery).toHaveBeenCalledWith(
			['contacts+jane doe'],
			expect.any(Function),
			{ enabled: true }
		);
	});
});

describe('searchContacts helper function', () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('should make correct API call with search parameter', async () => {
		const mockResponse = { data: mockContactsData };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValue(mockResponse);

		// We need to import and test the searchContacts function
		// Since it's not exported, we'll test it through the hook's query function
		const searchTerm = 'test';
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		let queryFn: any;

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockImplementation((key, fn) => {
			queryFn = fn;
			return {
				data: mockContactsData,
				isLoading: false,
				isError: false,
				error: null,
			};
		});

		renderHook(() => useGetContacts(searchTerm));

		// Call the query function that was passed to useQuery
		await queryFn();

		expect(axios.get).toHaveBeenCalledWith(
			`/api/3/contacts?meta=all&include=contactData,scoreValues,accountContacts,accountContacts.account&search=${searchTerm}`
		);
	});

	it('should not make API call when search parameter is empty', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockImplementation((key, fn, options) => {
			// Verify that the query is disabled when search is empty
			expect(options.enabled).toBe(false);
			return {
				data: undefined,
				isLoading: false,
				isError: false,
				error: null,
			};
		});

		renderHook(() => useGetContacts(''));

		// Verify that axios.get was never called when search is empty
		expect(axios.get).not.toHaveBeenCalled();
	});

	it('should make API call when search parameter is provided', async () => {
		const mockResponse = { data: mockContactsData };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValue(mockResponse);

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		let queryFn: any;

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockImplementation((key, fn, options) => {
			queryFn = fn;
			// Verify that the query is enabled when search has a value
			expect(options.enabled).toBe(true);
			return {
				data: mockContactsData,
				isLoading: false,
				isError: false,
				error: null,
			};
		});

		renderHook(() => useGetContacts('test'));

		// Call the query function that was passed to useQuery
		await queryFn();

		expect(axios.get).toHaveBeenCalledWith(
			'/api/3/contacts?meta=all&include=contactData,scoreValues,accountContacts,accountContacts.account&search=test'
		);
	});

	it('should handle API errors correctly', async () => {
		const mockError = {
			response: {
				data: { message: 'API Error' },
			},
		};
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockRejectedValue(mockError);

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		let queryFn: any;

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockImplementation((key, fn) => {
			queryFn = fn;
			return {
				data: undefined,
				isLoading: false,
				isError: true,
				error: mockError.response.data,
			};
		});

		renderHook(() => useGetContacts('test'));

		// Test that the query function rejects with the correct error
		await expect(queryFn()).rejects.toEqual({ message: 'API Error' });
	});

	it('should use default empty string parameter when searchContacts is called without arguments', async () => {
		const mockResponse = { data: mockContactsData };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValue(mockResponse);

		// Call searchContacts without any arguments to test the default parameter
		await searchContacts();

		// The default parameter should result in an empty search string
		expect(axios.get).toHaveBeenCalledWith(
			'/api/3/contacts?meta=all&include=contactData,scoreValues,accountContacts,accountContacts.account&search='
		);
	});
});
