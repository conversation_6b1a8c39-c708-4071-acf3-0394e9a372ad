import { getContactIdFromUrl } from '../utils/utils';
import {
	DynamicVariable,
	getContactDynamicVariables,
} from '@src/api/contactDynamicVariables';
import {
	createToast,
	ToastDuration,
} from '@activecampaign/camp-components-toast';
import {
	useQuery,
	useMutation,
	queryClient,
	UseMutationResult,
} from '@activecampaign/platform-core-queries';
import { regenerateContactDynamicVariable } from '../api/regenerateDynamicVariable';
import { useTranslation } from '@activecampaign/core-translations-client';

interface UseDynamicVariablesReturn {
	loading: boolean;
	dynamicVariables: DynamicVariable[];
	regenerateDynamicVariable: UseMutationResult<DynamicVariable, Error, string>;
}

export const useDynamicVariables = (
	contactId: ReturnType<typeof getContactIdFromUrl>
): UseDynamicVariablesReturn => {
	const { t } = useTranslation();
	const { data, isLoading, isFetching } = useQuery(
		['dynamicVariables', contactId],
		async () => {
			const response = await getContactDynamicVariables(contactId);
			return response.dynamicVariables;
		},
		{
			enabled: !!contactId,
			onError: () => {
				createToast({
					description: t(
						'fields:contacts:dynamic-variables:edit:error-message-description'
					),
					appearance: 'danger',
					title: t('fields:contacts:dynamic-variables:edit:error-message'),
					duration: ToastDuration.Standard,
				});
			},
			retry: false,
		}
	);

	const regenerateDynamicVariable = useMutation(
		async (variableId: string) => {
			return regenerateContactDynamicVariable(contactId, variableId);
		},
		{
			onSuccess: () => {
				queryClient.invalidateQueries({
					queryKey: ['dynamicVariables', contactId],
				});
				createToast({
					description: t(
						'fields:contacts:dynamic-variables:regenerate-success-description'
					),
					appearance: 'success',
					title: t(
						'fields:contacts:dynamic-variables:regenerate-success-title'
					),
					duration: ToastDuration.Short,
				});
			},
			onError: (err: Error) => {
				createToast({
					description: err.message,
					appearance: 'danger',
					title: t('fields:contacts:dynamic-variables:regenerate-error-title'),
					duration: ToastDuration.Standard,
				});
			},
		}
	);

	return {
		loading: isLoading || isFetching,
		dynamicVariables: data,
		regenerateDynamicVariable,
	};
};
