import React from 'react';
import Styled from '@activecampaign/camp-components-styled';
import Flex from '@activecampaign/camp-components-flex';
import Text from '@activecampaign/camp-components-text';
import { InlineEdit } from '../inline-edit/inline-edit';
import { editDynamicVariable } from '@src/api/editDynamicVariable';
import { useMutation } from '@tanstack/react-query';
// import { RefreshMedium } from '@camp/icon';
// import { SemanticColors } from '@camp/tokens';
import { DynamicVariable } from 'api/contactDynamicVariables';
// import Tooltip from '@activecampaign/camp-components-tooltip';
import { useTranslation } from '@activecampaign/core-translations-client';
// import LoadingIndicator from '@activecampaign/camp-components-loading-indicator';

import { styles } from '../../styles/styles';
import {
	createToast,
	ToastDuration,
} from '@activecampaign/camp-components-toast';
// import { useDynamicVariables } from '../../hooks/useDynamicVariables';
// import { AiLoadingCard } from '@activecampaign/camp-components-ai';
import { queryClient } from '@activecampaign/platform-core-queries';

export interface AccordionRowProps {
	item: DynamicVariable;
	contactId: string;
}

const AccordionRow: React.FC<AccordionRowProps> = ({ contactId, item }) => {
	const { t } = useTranslation();
	// const { regenerateDynamicVariable } = useDynamicVariables(contactId);

	// const handleRegenerate = (): void => {
	// 	regenerateDynamicVariable.mutate(item.id);
	// };

	const { mutate: handleSaveEdit, isLoading } = useMutation({
		mutationFn: (value: string) =>
			editDynamicVariable({
				contactId,
				variableId: String(item.id),
				value,
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ['dynamicVariables', contactId],
			});
			createToast({
				appearance: 'success',
				title: t('fields:contacts:dynamic-variables:edit:success-message'),
				duration: ToastDuration.Standard,
			});
		},
		onError: () => {
			createToast({
				description: t(
					'fields:contacts:dynamic-variables:edit:error-message-description'
				),
				appearance: 'danger',
				title: t('fields:contacts:dynamic-variables:edit:error-message'),
				duration: ToastDuration.Standard,
			});
		},
	});

	return (
		<Styled
			data-testid={`dynamic-variable-field-${item.id}`}
			py="sp300"
			px="36px"
			pr={0}
			styles={styles.styledAccordionRowWrapper}
		>
			<Flex
				data-testid={`dynamic-variable-field-label-container-${item.id}`}
				pr="sp300"
				alignItems="center"
				styles={{
					...styles.styledAccordionRowTitleWrapper,
					...styles.styledFlexWidthSmall,
				}}
			>
				<Text
					data-testid={`dynamic-variable-label-${item.id}`}
					color="text/default"
					weight="fwSemiBold"
					styles={styles.longTextWrapper}
					title={item.variable_name}
				>
					{item.variable_name}
				</Text>
			</Flex>

			<Flex
				data-testid={`dynamic-variable-value-container-${item.id}`}
				alignItems="center"
				styles={styles.styledFlexWidthLarge}
			>
				<InlineEdit
					disabled={isLoading}
					onSave={handleSaveEdit}
					value={item.value}
					id={item.id}
				/>

				{/* <Flex
					alignItems="center"
					styles={{
						'& .c-tooltip-popover': {
							background: regenerateDynamicVariable.isLoading && 'transparent',
						},
					}}
				>
					<Tooltip
						usePortal={false}
						content={
							regenerateDynamicVariable.isLoading ? (
								<AiLoadingCard
									isLoading
									title={t(
										'fields:contacts:dynamic-variables:ai-loading-content'
									)}
									loadingIndicatorSize="medium"
									styles={styles.styledAiCard}
								/>
							) : (
								t('fields:contacts:dynamic-variables:regenerate-tooltip')
							)
						}
						appearance={regenerateDynamicVariable.isLoading ? 'light' : 'dark'}
						placement="right"
						tooltipStyles={{
							'& > .c-tooltip-container': {
								padding: regenerateDynamicVariable.isLoading && 0,
							},
						}}
						data-testid="regenerate-tooltip"
					>
						{regenerateDynamicVariable.isLoading ? (
							<Flex styles={styles.loaderWrapper}>
								<LoadingIndicator
									appearance="default"
									size="small"
									data-testid="dynamic-variables-accordion-title-loading-indicator"
								/>
							</Flex>
						) : (
							<RefreshMedium
								display="flex"
								cursor="pointer"
								fill={SemanticColors['icon-decorative']}
								data-testid={`dynamic-variable-refresh-icon-${item.id}`}
								onClick={handleRegenerate}
							/>
						)}
					</Tooltip>
				</Flex> */}
			</Flex>
		</Styled>
	);
};

export default AccordionRow;
