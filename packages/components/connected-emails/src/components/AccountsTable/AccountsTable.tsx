import React, { useState } from 'react';
import { useTranslation } from '@activecampaign/core-translations-client';
import { Table } from '@activecampaign/camp-components-table';
import { getCoreRowModel, useReactTable } from '@tanstack/react-table';
import Columns from './Columns';
import { useConnectedEmailsContext } from '@src/context/ConnectedEmailsContext';
import LoadingIndicator from '@activecampaign/camp-components-loading-indicator';
import Flex from '@activecampaign/camp-components-flex';
import { useEmailConnections } from '@src/hooks/useEmailConnections';
import TablePaginator from '@src/components/TablePagination/TablePaginator';
import Styled from '@activecampaign/camp-components-styled';
import { PAGE_LIMIT } from '@src/connected-emails.constants';
import { ConnectedEmail } from 'types';
import { queryClient } from '@activecampaign/platform-core-queries';

const AccountsTable: React.FC = () => {
	const { t } = useTranslation();
	const [activePage, setActivePage] = useState<number>(0);

	const { isLoading, total, refetch } = useEmailConnections({ activePage });
	const { connectedEmails, setConnectedEmails } = useConnectedEmailsContext();

	const onRemoveRow = (connectedEmailId: number): void => {
		let lastRowOnPage = false;
		if (connectedEmails.length === 1) {
			lastRowOnPage = true;
		}
		setConnectedEmails((connectedEmails) =>
			connectedEmails.filter(
				(connectedEmail) => connectedEmail.id !== connectedEmailId
			)
		);
		// Invalidate the cache for the current page and refetch the data
		// useEmailConnections hook will refetch the data and update the connectedEmails state
		queryClient
			.invalidateQueries({
				queryKey: ['emailConnections', activePage],
			})
			.then(() => {
				refetch();
			});
		if (lastRowOnPage && activePage >= 1) {
			queryClient
				.invalidateQueries({
					queryKey: ['emailConnections', activePage - 1],
				})
				.then(() => {
					refetch();
				});
			setActivePage((activePage) => activePage - 1);
		}
	};
	const onUpdateRow = (connectedEmail: ConnectedEmail): void => {
		setConnectedEmails((connectedEmails) => {
			const index = connectedEmails.findIndex(
				({ id }) => id === connectedEmail.id
			);
			if (index === -1) {
				return connectedEmails;
			}
			const newConnectedEmails = [...connectedEmails];
			newConnectedEmails[index] = connectedEmail;
			return newConnectedEmails;
		});
	};

	const table = useReactTable({
		data: connectedEmails,
		columns: Columns({ onRemoveRow, onUpdateRow }),
		getCoreRowModel: getCoreRowModel(),
		getRowId: (row) => String(row.id),
	});

	return (
		<>
			{isLoading ? (
				<Flex justifyContent="center" data-testid="loading-indicator">
					<LoadingIndicator size="medium" />
				</Flex>
			) : (
				<Table
					table={table}
					tableName={t('settings:deals:connected-emails:header')}
				/>
			)}
			<Flex justifyContent="center">
				<Styled styles={{ paddingTop: 'sp700' }}>
					<TablePaginator
						paginationVisible={total > PAGE_LIMIT}
						active={activePage}
						setActive={setActivePage}
						pageCount={Math.ceil(total / PAGE_LIMIT)}
						textOnly={false}
					/>
				</Styled>
			</Flex>
		</>
	);
};

export default AccountsTable;
