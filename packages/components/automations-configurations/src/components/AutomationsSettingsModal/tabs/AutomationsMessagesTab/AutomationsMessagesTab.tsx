import React, {
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import Flex from '@activecampaign/camp-components-flex';
import {
	useGetAutomationCampaignMessages,
	useDeleteCampaign,
	useEditSender,
	useSendTestEmails,
	useIntersectionObserver,
} from '../../../../hooks';
import AutomationsMessagesTabSkeleton from './AutomationsMessagesTabSkeleton';
import Checkbox from '@activecampaign/camp-components-checkbox';
import Button from '@activecampaign/camp-components-button';
import {
	AutomationBlock,
	DynamicObj,
	useQueryClient,
} from '@activecampaign/platform-core-queries';
import { useTranslation } from '@activecampaign/core-translations-client';
import {
	DeleteCampaignSection,
	EditSenderSection,
	TestEmailSection,
} from '../../modal-sections';
import {
	ToastDuration,
	createToast,
} from '@activecampaign/camp-components-toast';
import Text from '@activecampaign/camp-components-text';
import { useAutomationsSettingsModal } from '../../../AutomationsSettingsModalContext';
import {
	ModalSections,
	EditSenderForm,
	useEditSenderValue,
} from '../../../../automations-settings-modal-constants';
import { useGlobalData } from '@activecampaign/platform-core-queries';
import { SpotIllustration } from '@activecampaign/camp-components-illustration';
import { emailSpot } from '@activecampaign/camp-tokens-illustration';
import LoadingIndicator from '@activecampaign/camp-components-loading-indicator';
import AutomationMessageCard from './AutomationMessageCard';

type AutomationsMessagesTabProps = {
	automationId: number;
	isOpen: boolean;
	modifyBlockCallback: (blockId: string, props: DynamicObj) => void;
	focusNodeInCanvas: (blockId: number) => void;
	campaignsSorted: number[];
};

/**
 *
 * @param blocks
 * @returns a dictionary which key is the campaignId and value is the corresponding block.
 */
const getCampaignBlocks = (
	blocks: AutomationBlock[]
): {
	[key: number]: string;
} => {
	const result = {};
	blocks
		?.filter((block) => block.type === 'send')
		.forEach(
			(block) => (result[Number(block.params?.campaignid)] = Number(block.id))
		);
	return result;
};
const AutomationsMessagesTab: React.FC<AutomationsMessagesTabProps> = (
	props: AutomationsMessagesTabProps
) => {
	const { t } = useTranslation();

	const { checkedCampaigns, setCheckedCampaigns, blocks } =
		useAutomationsSettingsModal();

	const automationCampaignBlocks = useMemo(
		() => getCampaignBlocks(blocks),
		[blocks]
	);

	const queryClient = useQueryClient();
	const {
		data: sortedCampaigns,
		isLoading: isLoadingCampaignMessages,
		refetch: refetchCampaignMessages,
		loadMore,
		hasMore,
		total: emailCount,
		isFetchingMore,
		fetchAll,
	} = useGetAutomationCampaignMessages(
		props.automationId,
		props.campaignsSorted
	);

	const triggerRef = useRef();

	const pivotIsOnScreen = useIntersectionObserver({
		ref: triggerRef,
		threshold: 1,
	});

	const { data: globalData } = useGlobalData();

	const { admin } = globalData;

	const [modalSection, setModalSection] = useState<ModalSections>(null);

	const [campaignToDelete, setCampaignToDelete] = useState<number>();

	//Declaration of mutations
	const { mutateAsync: deleteCampaign, isLoading: isDeleteCampaignLoading } =
		useDeleteCampaign();

	const { mutateAsync: mutateEditSender, isLoading: isEditSenderLoading } =
		useEditSender();

	const { mutateAsync: sendTestEmails, isLoading: isSendTestEmailsLoading } =
		useSendTestEmails();

	const clearModalSection = (): void => setModalSection(null);

	const onDeleteCampaign = useCallback(async (): Promise<void> => {
		deleteCampaign(campaignToDelete).then((response: Response) => {
			if (response.status === 200) {
				const blockId = automationCampaignBlocks[campaignToDelete];
				createToast({
					appearance: 'success',
					title: t('automations:view-emails:delete-section:success'),
					titleTestId: 'delete-section-toast-success',
					duration: ToastDuration.Standard,
				});
				setCampaignToDelete(undefined);
				clearModalSection();
				blockId &&
					props.modifyBlockCallback(blockId.toString(), {
						campaignid: 0,
						editingcreateblock: false,
					});
				refetchCampaignMessages();
			} else {
				createToast({
					appearance: 'danger',
					title: t('automations:view-emails:delete-section:error'),
					titleTestId: 'delete-section-toast-error',
					duration: ToastDuration.Standard,
				});
			}
		});
	}, [
		deleteCampaign,
		campaignToDelete,
		t,
		props,
		refetchCampaignMessages,
		automationCampaignBlocks,
	]);

	const campaignIsOrphan = (campaignId: number): boolean => {
		return !automationCampaignBlocks?.hasOwnProperty(Number(campaignId));
	};

	const onCardCheckClick = (campaignId: number): void => {
		if (checkedCampaigns.includes(campaignId)) {
			setCheckedCampaigns(
				checkedCampaigns.filter(
					(checkedCampaignId) =>
						Number(checkedCampaignId) !== Number(campaignId)
				)
			);
		} else {
			setCheckedCampaigns([...checkedCampaigns, Number(campaignId)]);
		}
	};

	const onCardClick = (campaignId: number): void => {
		const blockPerCampaign = getCampaignBlocks(blocks);
		const associatedBlock = blockPerCampaign[campaignId];
		associatedBlock && props.focusNodeInCanvas(Number(associatedBlock));
		setCheckedCampaigns([campaignId]);
	};

	const onSelectAllClick = (): void => {
		if (checkedCampaigns.length === emailCount) {
			setCheckedCampaigns([]);
		} else {
			setCheckedCampaigns(
				sortedCampaigns.automationCampaignsData.map(({ id }) => Number(id))
			);
			fetchAll();
		}
	};

	const selectAllIsChecked = checkedCampaigns.length === emailCount;
	const checkedCampaignsData = sortedCampaigns?.campaignMessages.filter(
		(campaign) => checkedCampaigns.includes(Number(campaign.id))
	);

	//Edit sender functions
	const onEditSenderSaveClick = useCallback(
		async (formValues: EditSenderForm): Promise<void> => {
			const emailDataToUpdate = checkedCampaignsData.map((email) => {
				return {
					id: email.messages[0]?.messageid,
					fromname: formValues.fromName,
					fromemail: formValues.fromEmail,
					reply2: formValues.replyTo,
				};
			});
			const promisesResults = (await mutateEditSender(
				emailDataToUpdate
			)) as useEditSenderValue[];
			const promisesStatuses = promisesResults.map(
				(messagePromise) => messagePromise.status
			);

			queryClient.invalidateQueries({
				queryKey: ['getMessageByCampaignId'],
			});

			if (promisesStatuses.includes('fulfilled')) {
				refetchCampaignMessages();
				createToast({
					appearance: 'success',
					title: t('automations:view-emails:edit-sender-success'),
					titleTestId: 'edit-sender-toast-success',
					duration: ToastDuration.Standard,
				});
			} else {
				createToast({
					appearance: 'danger',
					title: t('automations:view-emails:edit-sender-error'),
					titleTestId: 'edit-sender-toast-error',
					duration: ToastDuration.Standard,
				});
			}
			clearModalSection();
		},
		[
			checkedCampaignsData,
			mutateEditSender,
			t,
			queryClient,
			refetchCampaignMessages,
		]
	);

	const handleTestSectionSendClick = useCallback(
		async (emailStringList: string): Promise<void> => {
			const emailList = emailStringList.split(',').map((email) => email.trim());
			await Promise.all(
				emailList.flatMap((testEmail) =>
					checkedCampaignsData
						.filter(({ id }) => typeof id !== 'undefined')
						.map(async ({ messages, id }) => {
							const { fromname, fromemail, subject, messageid } = messages[0];
							return sendTestEmails({
								testEmail,
								fromName: fromname,
								fromEmail: fromemail,
								subject,
								campaignId: id.toString(),
								messageId: messageid,
							})
								.then((value) => ({
									status: 'fulfilled',
									value,
								}))
								.catch((value) => ({
									status: 'rejected',
									value,
								}));
						})
				)
			).then((results) => {
				const promisesStatuses = results.map(
					(messagePromise) => messagePromise.status
				);
				if (promisesStatuses.includes('fulfilled')) {
					createToast({
						appearance: 'success',
						title: t('automations:view-emails:test-section:success'),
						titleTestId: 'test-section-toast-success',
						duration: ToastDuration.Standard,
					});
				} else {
					createToast({
						appearance: 'danger',
						title: t('automations:view-emails:test-section:error'),
						titleTestId: 'test-section-toast-error',
						duration: ToastDuration.Standard,
					});
				}
			});
			clearModalSection();
		},
		[checkedCampaignsData, sendTestEmails, t]
	);

	useEffect(() => {
		if (checkedCampaigns.length === 1 && !isLoadingCampaignMessages) {
			document
				.querySelector(`#automation-message-card-${checkedCampaigns[0]}`)
				?.scrollIntoView({ behavior: 'smooth', block: 'center' });
		}
	}, [isLoadingCampaignMessages, checkedCampaigns]);

	useEffect(() => {
		if (props.isOpen) {
			if (checkedCampaigns.length === 1) {
				fetchAll();
			}
			refetchCampaignMessages();
		}
		// eslint-disable-next-line
	}, [props.isOpen, refetchCampaignMessages]);

	useEffect(() => {
		if (hasMore && pivotIsOnScreen) {
			loadMore();
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [pivotIsOnScreen, hasMore]);

	return (
		<>
			<>
				<Flex
					direction="column"
					styles={{
						gap: 'sp600',
						...(emailCount === 0
							? {
									height: '80%',
									alignItems: 'center',
									justifyContent: 'center',
							  }
							: {}),
					}}
					data-testid="automation-messages-tab"
				>
					{emailCount === 0 && !isLoadingCampaignMessages ? (
						<>
							<SpotIllustration size="medium" use={emailSpot} />
							<Text.Body
								size="small"
								data-testid={'no-emails-text'}
								dangerouslySetStyles={{ color: '#5F667E' }}
								styles={{ maxWidth: '250px', textAlign: 'center' }}
							>
								{/* Looks like you haven't created any messages in this automation */}
								{t('automations:automations-configurations:no-emails')}
							</Text.Body>
						</>
					) : (
						<>
							<Flex
								direction="column"
								disabled={isLoadingCampaignMessages}
								styles={{
									gap: 'sp600',
									position: 'sticky',
									top: '74px',
									paddingBottom: '5px',
									paddingTop: 'sp600',
									background: 'white',
									width: '100%',
									zIndex: 10,
								}}
							>
								<Text.Heading size="medium" data-testid={'email-count-header'}>
									{t(
										isLoadingCampaignMessages
											? 'automations:view-emails:view-emails-modal-loading'
											: emailCount === 1
											? 'automations:view-emails:view-emails-modal-subtitle-sing'
											: 'automations:view-emails:view-emails-modal-subtitle-plural',
										{ count: emailCount }
									)}
								</Text.Heading>

								<Flex
									direction="row"
									justifyContent="space-between"
									alignItems="center"
									disabled={isLoadingCampaignMessages}
								>
									<Checkbox
										onChange={onSelectAllClick}
										checked={selectAllIsChecked}
										label={t('automations:configurations:select-all')}
										data-testid={`select-all-checkbox`}
										disabled={isLoadingCampaignMessages}
									/>
									<Flex styles={{ gap: 'sp400' }}>
										<Button.Outline
											data-testid={'test-all-senders-button'}
											size="small"
											disabled={isLoadingCampaignMessages}
											onClick={(): void => {
												checkedCampaigns.length === 0 && onSelectAllClick();
												setModalSection(ModalSections.TestEmailModal);
											}}
										>
											{t(
												checkedCampaigns.length === 0 ||
													checkedCampaigns.length === emailCount
													? 'automations:configurations:test-all-senders-button'
													: 'automations:configurations:test-emails'
											)}
										</Button.Outline>
										<Button.Outline
											data-testid={'edit-sender-button'}
											size="small"
											disabled={isLoadingCampaignMessages}
											onClick={(): void => {
												checkedCampaigns.length === 0 && onSelectAllClick();
												setModalSection(ModalSections.EditSenderModal);
											}}
										>
											{/* Edit all */}
											{checkedCampaigns.length === 0 ||
											checkedCampaigns.length ===
												sortedCampaigns?.campaignMessages.length
												? t(
														'automations:configurations:edit-all-senders-button'
												  )
												: t('automations:configurations:edit-sender-button')}
										</Button.Outline>
									</Flex>
								</Flex>
							</Flex>
							{isLoadingCampaignMessages ? (
								<AutomationsMessagesTabSkeleton />
							) : (
								<Flex
									direction="column"
									styles={{
										gap: 'sp600',
										padding: '0 sp100',
									}}
								>
									{sortedCampaigns.campaignMessages.map((campaign) => (
										<AutomationMessageCard
											key={campaign.id}
											campaign={campaign}
											isOrphan={campaignIsOrphan(Number(campaign.id))}
											handleModalSection={setModalSection}
											onCampaignDelete={setCampaignToDelete}
											onCardCheckClick={onCardCheckClick}
											onCardClick={onCardClick}
											isChecked={checkedCampaigns.includes(Number(campaign.id))}
										/>
									))}
								</Flex>
							)}
						</>
					)}
				</Flex>
				{modalSection == ModalSections.TestEmailModal && (
					<TestEmailSection
						onBackClick={clearModalSection}
						onSendClick={handleTestSectionSendClick}
						isLoading={isSendTestEmailsLoading}
						initialEmailList={admin?.email}
						title={t(
							checkedCampaigns.length > 1
								? 'automations:view-emails:view-emails-modal-test-title-plural'
								: 'automations:view-emails:view-emails-modal-test-title-sing',
							{
								emailAmount: checkedCampaigns.length,
							}
						)}
					/>
				)}
				{modalSection == ModalSections.DeleteModal && (
					<DeleteCampaignSection
						onDeleteClick={onDeleteCampaign}
						onBackClick={clearModalSection}
						isLoading={isDeleteCampaignLoading}
					/>
				)}
				{modalSection == ModalSections.EditSenderModal && (
					<EditSenderSection
						modalTitle={t(
							checkedCampaigns.length === 1
								? 'automations:view-emails:edit-sender-modal-title-sing'
								: 'automations:view-emails:edit-sender-modal-title-plural',
							{
								emailCount: checkedCampaigns.length,
							}
						)}
						onSaveClick={onEditSenderSaveClick}
						onBackClick={clearModalSection}
						isEditSenderLoading={isEditSenderLoading}
						defaultFormValues={{
							fromEmail:
								checkedCampaignsData.length === 1
									? checkedCampaignsData[0]?.messages[0]?.fromemail
									: admin?.email,
							replyTo:
								checkedCampaignsData.length === 1
									? checkedCampaignsData[0]?.messages[0]?.reply2
									: admin?.email,
							fromName:
								checkedCampaignsData.length === 1
									? checkedCampaignsData[0]?.messages[0]?.fromname
									: admin?.fullname,
							fromNameHasPers: false,
							fromEmailHasPers: false,
						}}
					/>
				)}
			</>
			<div
				data-testid={'pivot'}
				style={{
					width: '100%',
					height: hasMore ? '75px' : '0px',
					display: 'flex',
					justifyContent: 'center',
					alignItems: 'center',
				}}
				ref={triggerRef}
			>
				{isFetchingMore && (
					<LoadingIndicator
						data-testid="loading-indicator"
						size="medium"
						appearance="default"
					/>
				)}
			</div>
		</>
	);
};

export default AutomationsMessagesTab;
