import { renderHook, act } from '@testing-library/react-hooks';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import {
	updateAutomationTracking,
	useUpdateAutomationTracking,
} from '../useUpdateAutomationTracking';
import { DynamicObj } from '@activecampaign/platform-core-queries';
import { AutomationSettingsTrackingOptions } from './../../automations-settings-modal-constants';

jest.mock('axios');
jest.mock('@tanstack/react-query');

const mockedAxiosPut = axios.put as jest.MockedFunction<typeof axios.put>;
const mockedUseMutation = useMutation as jest.MockedFunction<
	typeof useMutation
>;
const mockedUseQueryClient = useQueryClient as jest.MockedFunction<
	typeof useQueryClient
>;

describe('updateAutomationTracking', () => {
	it('should send a PUT request with the correct URL and payload', async () => {
		const mockPayload = {
			relationType: AutomationSettingsTrackingOptions.Initiative,
			relationId: 1,
		};

		mockedAxiosPut.mockResolvedValueOnce({ data: 'success' });

		const response = await updateAutomationTracking(1, mockPayload);

		expect(mockedAxiosPut).toHaveBeenCalledWith(
			'/api/3/automationRelations/1/tracking',
			mockPayload
		);
		expect(response).toEqual({ data: 'success' });
	});
});

describe('useUpdateAutomationTracking', () => {
	it('should call useMutation with the correct mutate function and onSuccess handler', () => {
		const mockInvalidateQueries = jest.fn();
		const mockQueryClient = { invalidateQueries: mockInvalidateQueries };

		mockedUseQueryClient.mockReturnValue(mockQueryClient);
		const mockMutateFn = jest.fn();

		mockedUseMutation.mockImplementation((mutateFn, options) => {
			mockMutateFn.mockImplementation(mutateFn);
			return { mutate: mockMutateFn, ...options };
		});

		const { result } = renderHook(() => useUpdateAutomationTracking(1));

		const mockPayload = {
			relationType: AutomationSettingsTrackingOptions.Initiative,
			relationId: 1,
		};

		act(() => {
			result.current.mutate(mockPayload);
		});

		expect(mockMutateFn).toHaveBeenCalledWith(mockPayload);
	});

	it('should invalidate queries on success', async () => {
		const mockInvalidateQueries = jest.fn();
		const mockQueryClient = { invalidateQueries: mockInvalidateQueries };
		const mockPayload = {
			relationType: AutomationSettingsTrackingOptions.Initiative,
			relationId: 1,
		};

		mockedUseQueryClient.mockReturnValue(mockQueryClient);

		mockedUseMutation.mockImplementation((mutateFn, options) => {
			return {
				mutate: async (payload): DynamicObj => {
					await options.onSuccess({}, payload);
				},
			};
		});

		const { result } = renderHook(() => useUpdateAutomationTracking(1));

		act(() => {
			result.current.mutate(mockPayload);
		});

		expect(mockInvalidateQueries).toHaveBeenCalledWith({
			queryKey: ['automationTracking', 1],
		});
	});
});
