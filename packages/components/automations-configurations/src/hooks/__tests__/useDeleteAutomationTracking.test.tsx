import { renderHook, act } from '@testing-library/react-hooks';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import {
	deleteAutomationTracking,
	useDeleteAutomationTracking,
} from '../useDeleteAutomationTracking';
import { DynamicObj } from '@activecampaign/platform-core-queries';

jest.mock('axios');
jest.mock('@tanstack/react-query');

const mockedAxiosDelete = axios.delete as jest.MockedFunction<
	typeof axios.delete
>;
const mockedUseMutation = useMutation as jest.MockedFunction<
	typeof useMutation
>;
const mockedUseQueryClient = useQueryClient as jest.MockedFunction<
	typeof useQueryClient
>;

describe('deleteAutomationTracking', () => {
	it('should send a DELETE request with the correct URL', async () => {
		const automationId = 123;
		mockedAxiosDelete.mockResolvedValueOnce({ data: 'success' });

		const response = await deleteAutomationTracking(automationId);

		expect(mockedAxiosDelete).toHaveBeenCalledWith(
			'/api/3/automationRelations/123/tracking'
		);
		expect(response).toEqual({ data: 'success' });
	});
});

describe('useDeleteAutomationTracking', () => {
	it('should call useMutation with the correct mutate function', () => {
		const automationId = 123;
		const mockMutateFn = jest.fn();
		const mockInvalidateQueries = jest.fn();
		const mockQueryClient = { invalidateQueries: mockInvalidateQueries };

		mockedUseQueryClient.mockReturnValue(mockQueryClient);

		mockedUseMutation.mockImplementation((mutateFn, options) => {
			mockMutateFn.mockImplementation(mutateFn);
			return { mutate: mockMutateFn, ...options };
		});

		const { result } = renderHook(() =>
			useDeleteAutomationTracking(automationId)
		);

		act(() => {
			result.current.mutate();
		});

		expect(mockMutateFn).toHaveBeenCalled();
	});

	it('should invalidate queries on success', async () => {
		const automationId = 123;
		const mockInvalidateQueries = jest.fn();
		const mockQueryClient = { invalidateQueries: mockInvalidateQueries };

		mockedUseQueryClient.mockReturnValue(mockQueryClient);

		mockedUseMutation.mockImplementation((mutateFn, options) => {
			return {
				mutate: async (): Promise<DynamicObj> => {
					await options.onSuccess({});
					return {};
				},
			};
		});

		const { result } = renderHook(() =>
			useDeleteAutomationTracking(automationId)
		);

		act(() => {
			result.current.mutate();
		});

		expect(mockInvalidateQueries).toHaveBeenCalledWith({
			queryKey: ['automationTracking', automationId],
		});
	});
});
