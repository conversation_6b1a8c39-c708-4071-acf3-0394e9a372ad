import { renderHook, act } from '@testing-library/react-hooks';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { updateAutomation, useUpdateAutomation } from '../useUpdateAutomation';
import { DynamicObj } from '@activecampaign/platform-core-queries';
import { SeriesMultientry } from '../../types/Series';

jest.mock('axios');
jest.mock('@tanstack/react-query');

const mockedAxiosPut = axios.put as jest.MockedFunction<typeof axios.put>;
const mockedUseMutation = useMutation as jest.MockedFunction<
	typeof useMutation
>;
const mockedUseQueryClient = useQueryClient as jest.MockedFunction<
	typeof useQueryClient
>;

describe('updateAutomation', () => {
	it('should send a PUT request with the correct URL and payload', async () => {
		// test true values
		const mockPayload = {
			seriesid: '123',
			name: 'foo',
			description: 'bar',
			exit_on_unsubscribe: true,
			exit_on_conversion: true,
			multientry: SeriesMultientry.AnyNumberOfTimes,
		};

		mockedAxiosPut.mockResolvedValueOnce({ data: 'success' });

		const response = await updateAutomation(mockPayload);

		expect(mockedAxiosPut).toHaveBeenCalledWith('/api/3/automations/123', {
			automation: {
				name: 'foo',
				description: 'bar',
				exit_on_unsubscribe: '1',
				exit_on_conversion: '1',
				multientry: '1',
			},
		});
		expect(response).toEqual({ data: 'success' });

		// test false values
		const mockPayload2 = {
			seriesid: '123',
			name: 'foo',
			description: 'bar',
			exit_on_unsubscribe: false,
			exit_on_conversion: false,
			multientry: SeriesMultientry.AnyNumberOfTimes,
		};

		mockedAxiosPut.mockResolvedValueOnce({ data: 'success' });

		const response2 = await updateAutomation(mockPayload2);

		expect(mockedAxiosPut).toHaveBeenCalledWith('/api/3/automations/123', {
			automation: {
				name: 'foo',
				description: 'bar',
				exit_on_unsubscribe: '0',
				exit_on_conversion: '0',
				multientry: '1',
			},
		});
		expect(response2).toEqual({ data: 'success' });
	});
});

describe('useUpdateAutomation', () => {
	it('should call useMutation with the correct mutate function and onSuccess handler', () => {
		const mockInvalidateQueries = jest.fn();
		const mockQueryClient = { invalidateQueries: mockInvalidateQueries };

		mockedUseQueryClient.mockReturnValue(mockQueryClient);
		const mockMutateFn = jest.fn();

		mockedUseMutation.mockImplementation((mutateFn, options) => {
			mockMutateFn.mockImplementation(mutateFn);
			return { mutate: mockMutateFn, ...options };
		});

		const { result } = renderHook(() => useUpdateAutomation());

		const mockPayload = {
			seriesid: '123',
			exit_on_unsubscribe: true,
			exit_on_conversion: false,
			multientry: SeriesMultientry.AnyNumberOfTimes,
		};

		act(() => {
			result.current.mutate(mockPayload);
		});

		expect(mockMutateFn).toHaveBeenCalledWith(mockPayload);
	});

	it('should invalidate queries on success', async () => {
		const mockInvalidateQueries = jest.fn();
		const mockQueryClient = { invalidateQueries: mockInvalidateQueries };

		mockedUseQueryClient.mockReturnValue(mockQueryClient);

		mockedUseMutation.mockImplementation((mutateFn, options) => {
			return {
				mutate: async (payload): DynamicObj => {
					await options.onSuccess({}, payload);
				},
			};
		});

		const { result } = renderHook(() => useUpdateAutomation());

		act(() => {
			result.current.mutate({ seriesid: '123', exit_on_unsubscribe: true });
		});

		expect(mockInvalidateQueries).toHaveBeenCalledWith({
			queryKey: ['automations', '123'],
		});
	});
});
