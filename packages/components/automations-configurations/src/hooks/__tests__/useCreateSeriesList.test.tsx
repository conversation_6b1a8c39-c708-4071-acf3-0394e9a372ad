import { renderHook, act } from '@testing-library/react-hooks';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import useCreateSeriesList, { createSeriesList } from '../useCreateSeriesList';
import { DynamicObj } from '@activecampaign/platform-core-queries';

jest.mock('axios');
jest.mock('@tanstack/react-query');

const mockedAxiosPost = axios.post as jest.MockedFunction<typeof axios.post>;
const mockedUseMutation = useMutation as jest.MockedFunction<
	typeof useMutation
>;
const mockedUseQueryClient = useQueryClient as jest.MockedFunction<
	typeof useQueryClient
>;

describe('createSeriesList', () => {
	it('should send a POST request with the correct URL and payload', async () => {
		const mockPayload = {
			seriesid: '123',
			lists: ['list1', 'list2'],
		};

		mockedAxiosPost.mockResolvedValueOnce({ data: 'success' });

		const response = await createSeriesList(mockPayload);

		expect(mockedAxiosPost).toHaveBeenCalledWith(
			'/api/3/automations/serieslist/123',
			{ lists: ['list1', 'list2'] }
		);
		expect(response).toEqual({ data: 'success' });
	});
});

describe('useCreateSeriesList', () => {
	it('should call useMutation with the correct mutate function and onSuccess handler', () => {
		const mockInvalidateQueries = jest.fn();
		const mockQueryClient = { invalidateQueries: mockInvalidateQueries };

		mockedUseQueryClient.mockReturnValue(mockQueryClient);
		const mockMutateFn = jest.fn();

		mockedUseMutation.mockImplementation((mutateFn, options) => {
			mockMutateFn.mockImplementation(mutateFn);
			return { mutate: mockMutateFn, ...options };
		});

		const { result } = renderHook(() => useCreateSeriesList());

		const mockPayload = {
			seriesid: '123',
			lists: ['list1', 'list2'],
		};

		act(() => {
			result.current.mutate(mockPayload);
		});

		expect(mockMutateFn).toHaveBeenCalledWith(mockPayload);
	});

	it('should invalidate queries on success', async () => {
		const mockInvalidateQueries = jest.fn();
		const mockQueryClient = { invalidateQueries: mockInvalidateQueries };

		mockedUseQueryClient.mockReturnValue(mockQueryClient);

		mockedUseMutation.mockImplementation((mutateFn, options) => {
			return {
				mutate: async (payload): DynamicObj => {
					await options.onSuccess({}, payload);
				},
			};
		});

		const { result } = renderHook(() => useCreateSeriesList());

		act(() => {
			result.current.mutate({ seriesid: '123', lists: ['list1'] });
		});

		expect(mockInvalidateQueries).toHaveBeenCalledWith({
			queryKey: ['seriesLists', '123'],
		});
	});
});
