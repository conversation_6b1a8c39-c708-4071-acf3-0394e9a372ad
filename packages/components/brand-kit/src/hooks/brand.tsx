import {
	Brand,
	BrandPreviewRequest,
	BrandPreviewResponse,
	CreateBrandResponse,
} from '@src/types/brand';
import {
	UseMutationResult,
	UseQueryOptions,
	UseQueryResult,
	useMutation,
	useQuery,
	useQueryClient,
} from '@activecampaign/platform-core-queries';
import axios, { AxiosError, AxiosPromise, AxiosResponse } from 'axios';

import debounce from 'lodash.debounce';
import { useMemo } from 'react';

const NULL_UUID = '00000000-0000-0000-0000-000000000000';

export const useBrand = (id: string): UseQueryResult<Brand> => {
	return useQuery({
		queryKey: ['ai-brand', id],
		queryFn: async () => {
			const response = await axios.get(`/api/3/ai/brand/${id}`);
			return response.data;
		},
		enabled: !!id,
		retry: 3,
	});
};

export const useBrands = (
	options?: UseQueryOptions<Brand[]>
): UseQueryResult<Brand[]> => {
	return useQuery({
		queryKey: ['ai-brands'],
		queryFn: async () => {
			const response = await axios.get(`/api/3/ai/brand`);
			return response.data;
		},
		...options,
	});
};

export const useCreateBrand = (): UseMutationResult => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (payload): AxiosPromise<CreateBrandResponse> => {
			return axios.post(`/api/3/ai/brand`, {
				[typeof payload === 'string' ? 'websiteUrl' : 'brandData']: payload,
			});
		},
		onSuccess: (data) => {
			queryClient.setQueryData(
				['ai-brand', data.data.brand.id],
				data.data.brand
			);
			queryClient.invalidateQueries({
				queryKey: ['ai-brands'],
			});
		},
	});
};

export const useUpdateBrand = (): UseMutationResult => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (brand: Brand): AxiosPromise<Brand> =>
			axios.put(`/api/3/ai/brand/${brand.id}`, {
				brandData: brand.brandkit,
			}),
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ['ai-brand', data.data.id],
			});
			queryClient.resetQueries({
				queryKey: ['ai-preview-brand-templates'],
			});
			queryClient.resetQueries({
				queryKey: ['ai-brand-templates'],
			});
		},
		retry: (_count, error: AxiosError) => error.response.status === 409,
	});
};

export const useOverwriteBrand = (): UseMutationResult => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({ websiteUrl, id }): AxiosPromise<Brand> =>
			axios.put(`/api/3/ai/brand/${id}`, {
				websiteUrl,
			}),
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ['ai-brand', data.data.id],
			});
			queryClient.resetQueries({
				queryKey: ['ai-preview-brand-templates'],
			});
		},
	});
};

export const useDeleteBrand = (): UseMutationResult => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (brand: Brand): AxiosPromise<Brand> =>
			axios.delete(`/api/3/ai/brand/${brand.id}`),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ['ai-brands'],
			});
		},
	});
};

const getPreview = async ({
	id,
	brandkit,
	slug,
}): Promise<BrandPreviewResponse> => {
	const response: AxiosResponse<BrandPreviewResponse> = await axios.put(
		`/api/3/ai/templates/${id || NULL_UUID}/preview`,
		{
			brandkit,
			slug,
		}
	);

	return response.data;
};

export const useBrandPreview = (
	props: BrandPreviewRequest,
	options?: UseQueryOptions<BrandPreviewResponse>
): UseQueryResult<BrandPreviewResponse> => {
	const debouncedGetPreview = useMemo(
		() => debounce(getPreview, 200, { leading: true }),
		[]
	);

	return useQuery({
		queryKey: ['ai-brand-preview', props],
		queryFn: () => debouncedGetPreview(props),
		...options,
	});
};

export type Industry = {
	slug: string;
	title: string;
};

export const useBrandIndustries = (): UseQueryResult<{
	industries: Industry &
		{
			children: Industry[];
		}[];
}> => {
	return useQuery({
		queryKey: ['ai-brand-industries'],
		queryFn: async () => {
			const response = await axios.get(`/api/3/ai/industries`);
			return response.data;
		},
		retry: 3,
	});
};

export type Font = {
	family: string;
	stack: string;
	type: 'custom' | 'standard' | 'non-standard';
};

export const useBrandFonts = (): UseQueryResult<{
	fonts: Font[];
}> => {
	return useQuery({
		queryKey: ['ai-brand-fonts'],
		queryFn: async () => {
			const response = await axios.get(`/api/3/ai/fonts`);
			return response.data;
		},
		retry: 3,
	});
};
