export function invalidateLabelsRelationshipQueries(
	allRows = true,
	queryClient,
	origin
): void {
	queryClient.invalidateQueries({ queryKey: ['filtered-labels', origin] });
	queryClient.invalidateQueries({ queryKey: ['labels-index'] });
	queryClient.invalidateQueries({ queryKey: ['paginated-initiatives-labels'] });
	queryClient.invalidateQueries({ queryKey: ['paginated-labels'] });
	// this is for delete instances
	if (allRows) {
		queryClient.invalidateQueries({
			predicate: (query) =>
				// @ts-ignore
				query.queryKey[0].startsWith('row-labels'),
		});
	}
}
