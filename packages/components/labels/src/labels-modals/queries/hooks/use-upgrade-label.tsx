import { createToast } from '@activecampaign/camp-components-toast';
import {
	queryClient,
	useMutation,
	UseMutationResult,
} from '@activecampaign/platform-core-queries';
import { navigate } from '@activecampaign/platform-core-history';
import { upgradeLabel } from '../api';

interface UseUpgradeLabelArgs {
	handleDismissUpgrade: Function;
	onSuccessMessage: string;
	onErrorMessageTitle: string;
	onErrorMessageDescription: string;
	labelId: number;
}

export const useUpgradeLabel = ({
	handleDismissUpgrade,
	onSuccessMessage,
	labelId,
	onErrorMessageTitle,
	onErrorMessageDescription,
}: UseUpgradeLabelArgs): UseMutationResult => {
	return useMutation({
		mutationFn: ({ id, conversionId, targetGoalFieldValues }) => {
			return upgradeLabel(id, conversionId.toString(), targetGoalFieldValues);
		},
		onSuccess: async () => {
			// Invalidate the query to refetch the data
			queryClient.invalidateQueries({
				queryKey: ['all-labels'],
			});
			queryClient.invalidateQueries({
				queryKey: ['labels-index'],
			});
			navigate(`/app/initiatives/${labelId}`);

			handleDismissUpgrade();

			createToast({
				appearance: 'success',
				title: onSuccessMessage,
				duration: 'short',
			});
		},
		onError: () => {
			createToast({
				appearance: 'danger',
				title: onErrorMessageTitle,
				description: onErrorMessageDescription,
				duration: 'short',
			});
		},
	});
};
