import { useContext } from 'react';
import { createToast } from '@activecampaign/camp-components-toast';
import {
	queryClient,
	useMutation,
	UseMutationResult,
} from '@activecampaign/platform-core-queries';
import { useTranslation } from '@activecampaign/core-translations-client';
import { createLabel } from '../api';
import { LabelsModalsContext } from '../../context';
import {
	AUTOMATIONS,
	CAMPAIGNS,
	composedAddAutomationLabel,
	composedBulkAddAutomationLabel,
	FORMS,
	SMS,
} from '../../../shared';

export const useCreateLabel = (handleDismiss: Function): UseMutationResult => {
	const { t } = useTranslation();
	const { origin, recordId, records, acfSuccessHandlers, dropdownType } =
		useContext(LabelsModalsContext);

	function invalidator(): void {
		queryClient.invalidateQueries({
			queryKey: [`row-labels-${origin}-${recordId}`],
		});
		queryClient.invalidateQueries({ queryKey: ['filtered-labels', origin] });
	}

	function invalidateMany(): void {
		records.forEach((record) => {
			queryClient.refetchQueries({
				queryKey: [`row-labels-${origin}-${record}`],
			});
		});
		queryClient.invalidateQueries({
			queryKey: ['paginated-initiatives-labels'],
		});
		queryClient.invalidateQueries({ queryKey: ['filtered-labels', origin] });
	}

	return useMutation(createLabel, {
		onSuccess: async (res) => {
			queryClient.invalidateQueries({
				queryKey: ['labels-index'],
			});

			handleDismiss();

			createToast({
				appearance: 'success',
				title: t('generative-marketing:add-modal:toast:success'),
				duration: 'short',
			});

			if (origin === AUTOMATIONS) {
				if (recordId !== null && recordId !== undefined) {
					await composedAddAutomationLabel(
						recordId,
						res.data.label.id,
						invalidator,
						t(
							'generative-marketing:labels:add-to-automation:toast:success.translation.one'
						),
						t(
							'generative-marketing:add-to-automation:toast:error.translation.one'
						)
					);
				} else {
					await composedBulkAddAutomationLabel(
						records,
						res.data.label.id,
						t(
							'generative-marketing:labels:add-to-automation:toast:success.translation.other'
						),
						t(
							'generative-marketing:add-to-automation:toast:error.translation.other'
						)
					);
					invalidateMany();
				}
			}

			if (origin === CAMPAIGNS) {
				if (dropdownType === 'inline') {
					await acfSuccessHandlers.addInlineHandler(
						res.data.label.id,
						invalidator
					);
				} else {
					await acfSuccessHandlers.createLabelAddBulkHandler(res.data.label.id);
					invalidateMany();
				}
			}

			if (origin === FORMS) {
				if (dropdownType === 'inline') {
					await acfSuccessHandlers.addInlineHandler(
						res.data.label.id,
						invalidator
					);
				} else {
					await acfSuccessHandlers.createLabelAddBulkHandler(
						res.data.label.id,
						true,
						null
					);
					invalidateMany();
				}
			}

			if (origin === SMS) {
				if (dropdownType === 'inline') {
					await acfSuccessHandlers.addInlineHandler(res.data.label.id);
					invalidator();
				} else {
					await acfSuccessHandlers.createLabelAddBulkHandler(res.data.label.id);
					invalidateMany();
				}
			}

			queryClient.invalidateQueries({
				queryKey: ['paginated-initiatives-labels'],
			});
			queryClient.invalidateQueries({
				queryKey: ['paginated-labels'],
			});
			queryClient.refetchQueries({ queryKey: ['filtered-labels', origin] });
		},
		onError: () => {
			createToast({
				appearance: 'danger',
				title: t('generative-marketing:add-modal:toast:error'),
				duration: 'standard',
			});
		},
	});
};
