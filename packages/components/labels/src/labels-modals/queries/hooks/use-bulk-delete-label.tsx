import { createToast } from '@activecampaign/camp-components-toast';
import {
	queryClient,
	useMutation,
	UseMutationResult,
} from '@activecampaign/platform-core-queries';
import { useTranslation } from '@activecampaign/core-translations-client';
import { bulkDeleteLabel } from '../api';

/**
 * Custom hook for bulk deleting labels.
 *
 * @param handleDismissDelete - Function to handle dismissing the camp modal on success
 * @returns The mutation result of the bulk delete label operation.
 */
export const useBulkDeleteLabel = (
	handleDismissDelete: Function
): UseMutationResult => {
	const { t } = useTranslation();
	return useMutation(bulkDeleteLabel, {
		onSuccess: () => {
			// Invalidate the query to refetch the data
			queryClient.invalidateQueries({
				queryKey: ['paginated-initiatives-labels'],
			});
			queryClient.invalidateQueries({
				queryKey: ['paginated-labels'],
			});
			queryClient.invalidateQueries({
				queryKey: ['labels-index'],
			});
			queryClient.invalidateQueries({ queryKey: ['filtered-labels', origin] });

			handleDismissDelete();
			createToast({
				appearance: 'success',
				title: t(
					'generative-marketing:labels:delete-modal:toast:success.translation.other'
				),
				duration: 'short',
			});
		},
		onError: () => {
			createToast({
				appearance: 'danger',
				title: t('global:error'),
				duration: 'short',
			});
		},
	});
};
