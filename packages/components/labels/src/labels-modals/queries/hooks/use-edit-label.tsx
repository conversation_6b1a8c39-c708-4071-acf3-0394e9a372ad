import { createToast } from '@activecampaign/camp-components-toast';
import {
	queryClient,
	useMutation,
	UseMutationResult,
} from '@activecampaign/platform-core-queries';
import { useTranslation } from '@activecampaign/core-translations-client';
import { editLabel } from '../api';
import { LabelsModalsContext } from '../../context';
import { useContext } from 'react';

export const useEditLabel = (handleDismiss: Function): UseMutationResult => {
	const { t } = useTranslation();
	const { origin } = useContext(LabelsModalsContext);

	return useMutation({
		mutationFn: ({ id, label, color }) => {
			return editLabel(id, { label, color });
		},
		onSuccess: async () => {
			// Invalidate the query to refetch the data
			queryClient.invalidateQueries({
				predicate: (query) =>
					// @ts-ignore
					query.queryKey[0].startsWith('row-labels'),
			});
			queryClient.invalidateQueries({
				queryKey: ['labels-index'],
			});
			queryClient.invalidateQueries({ queryKey: ['filtered-labels', origin] });

			handleDismiss();

			createToast({
				appearance: 'success',
				title: t('generative-marketing:edit-modal:toast:success'),
				duration: 'short',
			});
		},
		onError: () => {
			createToast({
				appearance: 'danger',
				title: t('generative-marketing:edit-modal:toast:error'),
				duration: 'short',
			});
		},
	});
};
