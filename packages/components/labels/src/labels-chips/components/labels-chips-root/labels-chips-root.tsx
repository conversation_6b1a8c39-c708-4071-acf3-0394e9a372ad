import React, { useEffect, useState } from 'react';
import { ChipInteractive } from '@activecampaign/camp-components-chip';
import Styled from '@activecampaign/camp-components-styled';
import { useTranslation } from '@activecampaign/core-translations-client';
import { navigate } from '@activecampaign/platform-core-history';
import {
	queryClient,
	QueryClientProvider,
	useFeatureFlag,
} from '@activecampaign/platform-core-queries';
import { LabelsDropdown } from '../../../labels-dropdown';
import { LabelsModals } from '../../../labels-modals';
import {
	adjustColor,
	ACFHandlers,
	AUTOMATIONS,
	composedRemoveAutomationLabel,
	MODAL_TYPES,
	OriginType,
	LabelRelationship,
	CAMPAIGNS,
	FORMS,
	invalidateLabelsRelationshipQueries,
} from '../../../shared';
import styles from '../../labels-chips.styles';
import Skeleton from '@activecampaign/camp-components-skeleton';
import { invalidator } from '../../utils';

type LabelsChipsProps = {
	acfSuccessHandlers?: ACFHandlers;
	emberSuccessHandler?: Function;
	origin?: OriginType;
	recordId?: number | string;
	recordName?: string | null;
};

interface QueryClient {
	invalidateQueries: (queryKey: string | { queryKey: string[] }) => void;
}

export const LabelsChipsRoot: React.FC<LabelsChipsProps> = ({
	acfSuccessHandlers = null,
	emberSuccessHandler,
	origin,
	recordId = null,
	recordName = null,
}) => {
	const hasV2Dashboard = useFeatureFlag('generative-marketing-v-2-goals');
	const { t } = useTranslation();
	const [displayedChips, setDisplayedChips] = useState<
		Array<LabelRelationship>
	>([]);
	const [activeLable, setActiveLabel] = useState<LabelRelationship | null>(
		null
	);
	const [modalOpen, setModalOpen] = useState(false);
	const { EDIT, DELETE, REMOVE_ASSET } = MODAL_TYPES;
	const [modalType, setModalType] = useState<
		typeof DELETE | typeof EDIT | typeof REMOVE_ASSET
	>(null);
	const [previouslySelectedLoading, setPreviouslySelectedLoading] =
		useState(true);

	useEffect(() => {
		// remove this local storage value on unmount or nav away
		return (): void => {
			localStorage.removeItem(`${origin}-labels-${recordId}`);
		};
		// eslint-disable-next-line
	}, []);

	function handleClick(label: LabelRelationship): void {
		if (Number(label.isInitiative) === 1) {
			hasV2Dashboard
				? navigate(`/app/overview/business-goals/${label.id}`)
				: navigate(`/app/initiatives/${label.id}`);
		} else {
			setActiveLabel(label);
			setModalOpen(true);
			setModalType(EDIT);
		}
	}

	function openRemoveModal(label: LabelRelationship): void {
		setActiveLabel(label);
		setModalOpen(true);
		setModalType(REMOVE_ASSET);
	}

	function rowInvalidator(): void {
		queryClient.invalidateQueries({
			queryKey: [`row-labels-${origin}-${recordId}`],
		});
		invalidateLabelsRelationshipQueries(
			false,
			queryClient as QueryClient,
			origin
		);
	}

	async function handleDismiss(label: LabelRelationship): Promise<void> {
		if (origin === AUTOMATIONS) {
			await composedRemoveAutomationLabel(
				label.folderid,
				recordId,
				rowInvalidator,
				t(
					'generative-marketing:labels:remove-from-automation:toast:success.translation.one'
				),
				t('generative-marketing:remove-automation:toast:error.translation.one')
			);
		} else if (origin === CAMPAIGNS || origin === FORMS) {
			await acfSuccessHandlers.removeHandler(
				origin === CAMPAIGNS ? label.folderid : label.id,
				rowInvalidator
			);
		} else {
			await acfSuccessHandlers.removeHandler(+label.folderid);
			invalidator(queryClient as QueryClient, origin, recordId);
		}
	}

	return (
		<QueryClientProvider client={queryClient}>
			<Styled styles={styles.flex} data-testid="labels-chips">
				{previouslySelectedLoading && <Skeleton.Shape maxWidth="200px" />}
				{!previouslySelectedLoading &&
					displayedChips.length > 0 &&
					displayedChips
						.sort((a, b) => +a.id - +b.id)
						.map((l, idx) => (
							<Styled
								key={idx}
								styles={styles.adjustBackgroundColor(adjustColor(l.color))}
							>
								<ChipInteractive
									dismissTooltip={t('global:remove')}
									dismissTooltipPlacement="top"
									dismissTooltipAppearance="dark"
									onClick={(): void => handleClick(l)}
									onDismiss={(): Promise<void> | void =>
										+l.isInitiative === 1
											? openRemoveModal(l)
											: handleDismiss(l)
									}
								>
									{l.title || l.label}
								</ChipInteractive>
							</Styled>
						))}
				<LabelsDropdown
					acfSuccessHandlers={acfSuccessHandlers}
					dropdownType="inline"
					emberSuccessHandler={emberSuccessHandler}
					origin={origin}
					previouslySelectedLabels={displayedChips}
					recordId={recordId}
					setDisplayedChips={setDisplayedChips}
					setPreviouslySelectedLoading={setPreviouslySelectedLoading}
				/>
				{modalOpen && (
					<LabelsModals
						acfSuccessHandlers={acfSuccessHandlers}
						activeLabel={activeLable}
						emberSuccessHandler={emberSuccessHandler}
						modalOpen={modalOpen}
						modalType={
							modalType === EDIT
								? EDIT
								: modalType === DELETE
								? DELETE
								: REMOVE_ASSET
						}
						origin={origin}
						recordId={recordId}
						recordName={recordName}
						selectedLabels={[]}
						setModalOpen={setModalOpen}
						chipRemovalCallback={handleDismiss}
					/>
				)}
			</Styled>
		</QueryClientProvider>
	);
};
