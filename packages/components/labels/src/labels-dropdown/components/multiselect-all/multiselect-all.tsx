import React, {
	useCallback,
	useContext,
	useEffect,
	useMemo,
	useState,
} from 'react';
import { DropdownMultiselect } from '@activecampaign/camp-components-dropdown';
import Tooltip from '@activecampaign/camp-components-tooltip';
import { useTranslation } from '@activecampaign/core-translations-client';
import { DropdownTrigger } from '../dropdown-trigger';
import { ModalTrigger } from '../modal-trigger';
import {
	attachIntersectionObserver,
	defaultAutomationsToggle,
	determineSelectedLabels,
	inlineAutomationsToggle,
	observeLastListItem,
} from '../../../labels-dropdown/utils';
import { LabelsDropdownContext } from '../../../labels-dropdown/context';
import {
	AutomationRecord,
	AUTOMATIONS,
	CAMPAIGNS,
	DisplayedLabel,
	FORMS,
	Label,
	SMS,
} from '../../../shared';
import { queryClient } from '@activecampaign/platform-core-queries';
import { Icon } from '@activecampaign/camp-components-icon';
import { goalTrophy } from '@activecampaign/camp-tokens-icon';
import { PrimitiveColors } from '@camp/tokens';
import Flex from '@activecampaign/camp-components-flex';
import debounce from 'lodash.debounce';
import Text from '@activecampaign/camp-components-text';
import styles from './multiselect-all.styles';
import Styled from '@activecampaign/camp-components-styled';
import Skeleton from '@activecampaign/camp-components-skeleton';
import { LABELS_CHIP_DROPDOWN_TRIGGER_TEST_ID } from '../../types/ui';
// import { Indeterminate } from './indeterminate';

type MultiSelectAllProps = {
	labels: Array<Label> | null;
	previouslySelectedLabels: Array<DisplayedLabel>;
	setPreviouslySelectedLabels: Function;
	utilizeRenderTrigger: boolean;
	hasInitiatives: boolean;
	setParams?: Function;
	fetchNextPage?: Function;
	hasNextPage?: boolean;
	isLoading?: boolean;
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	generatedRecords?: Array<any>;
};

function TooltipWrapper({
	children,
	content,
	placement,
	shouldDisplay,
}): JSX.Element {
	if (shouldDisplay) {
		return (
			<Tooltip content={content} placement={placement}>
				<div data-testid="tooltip-wrapper">{children}</div>
			</Tooltip>
		);
	}

	return children;
}

export const MultiSelectAll: React.FC<MultiSelectAllProps> = ({
	labels,
	previouslySelectedLabels,
	setPreviouslySelectedLabels,
	utilizeRenderTrigger,
	hasInitiatives,
	setParams,
	fetchNextPage,
	hasNextPage,
	isLoading,
	generatedRecords = [],
}) => {
	const { t } = useTranslation();
	const { dropdownType, origin, recordId, records, acfSuccessHandlers } =
		useContext(LabelsDropdownContext);
	const [selectedLabels, setSelectedLabels] = useState<Array<DisplayedLabel>>(
		[]
	);
	const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
	const [lastItem, setLastItem] = useState<HTMLElement | null>(null);
	const [search, setSearch] = useState<string>('');

	useEffect(() => {
		if (!lastItem || search.length > 0 || !isDropdownOpen) return;

		attachIntersectionObserver(
			lastItem,
			hasNextPage as boolean,
			fetchNextPage as Function
		);
	}, [lastItem, search, hasNextPage, fetchNextPage, isDropdownOpen]);

	const automationToggleTranslations = {
		addSuccess: t(
			'generative-marketing:labels:add-to-automation:toast:success.translation.one'
		),
		addError: t(
			'generative-marketing:add-to-automation:toast:error.translation.one'
		),
		removeSuccess: t(
			'generative-marketing:labels:remove-from-automation:toast:success.translation.one'
		),
		removeError: t(
			'generative-marketing:remove-automation:toast:error.translation.one'
		),
	};

	const bulkAutomationToggleTranslations = {
		bulkAddSuccess: t(
			'generative-marketing:labels:add-to-automation:toast:success.translation.other'
		),
		bulkAddError: t(
			'generative-marketing:add-to-automation:toast:error.translation.other'
		),
		bulkRemoveSuccess: t(
			'generative-marketing:labels:remove-from-automation:toast:success.translation.other'
		),
		bulkRemoveError: t(
			'generative-marketing:remove-automation:toast:error.translation.other'
		),
	};

	const dLabels = useMemo(() => {
		if (!labels || !previouslySelectedLabels) return [];

		const occurrences =
			previouslySelectedLabels !== null &&
			previouslySelectedLabels?.reduce(
				(acc, { id }) => ({ ...acc, [id]: (acc[id] || 0) + 1 }),
				{}
			);

		return labels.map((l) => {
			const isIndeterminate =
				dropdownType === 'default' &&
				records.length > 1 &&
				occurrences[l.id] === 1;

			return {
				...l,
				indeterminate: isIndeterminate
					? true
					: occurrences[l.id] !== 1
					? false
					: false,
			};
		});
	}, [labels, previouslySelectedLabels, dropdownType, records]);

	const generatedDisplayedLabels = useMemo(() => {
		if (dLabels.length === 0) return [{ label: t('global:no-results') }];

		return hasInitiatives && dropdownType === 'inline'
			? [
					{
						children: dLabels.filter((d) => Number(d.isInitiative) === 1),
						label: t('generative-marketing:business-goals:global-nav'),
					},
					{
						children: dLabels.filter((d) => Number(d.isInitiative) === 0),
						label: t('generative-marketing:labels'),
					},
			  ]
			: dLabels;
	}, [dLabels, dropdownType, hasInitiatives, t]);

	const sLabels = useMemo(() => {
		return hasInitiatives && dropdownType === 'default'
			? dLabels.filter((l) =>
					[...previouslySelectedLabels, ...selectedLabels].find(
						(psl) => Number(psl.id) === Number(l.id)
					)
			  )
			: dLabels.filter((l) =>
					[...previouslySelectedLabels, ...selectedLabels].find(
						(psl) => Number(psl.id) === Number(l.id)
					)
			  );
	}, [
		dLabels,
		dropdownType,
		previouslySelectedLabels,
		hasInitiatives,
		selectedLabels,
	]);

	useEffect(() => {
		if (!isDropdownOpen) return;

		observeLastListItem(
			document.querySelector(
				`div[data-testid="multiselect-popover-${
					recordId !== null ? recordId : 'null'
				}"]`
			),
			setLastItem,
			dLabels.length === 0
		);
	}, [hasNextPage, dLabels, isDropdownOpen, recordId]);

	function invalidator(): void {
		queryClient.invalidateQueries({
			queryKey: [`row-labels-${origin}-${recordId}`],
		});
		queryClient.invalidateQueries({
			queryKey: ['filtered-labels', origin],
		});
	}

	// Automations only allow one initiative to be associated per automation due to complexity
	// in tracking mulitple initiatives against a single automation.
	const allowAddingInitiativeToAutomation = (
		isAlreadySelected: boolean
	): boolean => {
		if (isAlreadySelected) {
			return true;
		}

		const hasInitiativeSelected = sLabels.find(
			(selectedLabel) => Number(selectedLabel.isInitiative) === 1
		);
		return !hasInitiativeSelected;
	};

	async function handleSelect(selected): Promise<void> {
		const { selectedItem } = selected;

		const { isAlreadySelected, generatedSelectedLabels } =
			determineSelectedLabels(selectedItem, sLabels);

		if (dropdownType === 'inline') {
			if (origin === AUTOMATIONS) {
				if (!allowAddingInitiativeToAutomation(isAlreadySelected)) {
					return;
				}

				await inlineAutomationsToggle(
					isAlreadySelected,
					recordId,
					selectedItem,
					automationToggleTranslations,
					invalidator
				);
			}

			if (origin === CAMPAIGNS || origin === FORMS) {
				if (isAlreadySelected) {
					await acfSuccessHandlers.removeHandler(selectedItem.id, invalidator);
				} else {
					acfSuccessHandlers &&
						(await acfSuccessHandlers.addInlineHandler(
							selectedItem.id,
							invalidator
						));
				}
			}

			if (origin === SMS) {
				if (isAlreadySelected) {
					await acfSuccessHandlers.removeHandler(selectedItem.id);
				} else {
					acfSuccessHandlers &&
						(await acfSuccessHandlers.addInlineHandler(selectedItem.id));
				}
				invalidator();
			}
		}

		if (dropdownType === 'default') {
			setSelectedLabels(generatedSelectedLabels);

			if (isAlreadySelected) {
				setPreviouslySelectedLabels(
					previouslySelectedLabels.filter((l) => l.id !== selectedItem.id)
				);
			}

			if (origin === AUTOMATIONS) {
				await defaultAutomationsToggle(
					selectedItem,
					records as Array<AutomationRecord>,
					isAlreadySelected,
					bulkAutomationToggleTranslations
				);
			} else {
				if (isAlreadySelected) {
					acfSuccessHandlers &&
						(await acfSuccessHandlers.removeBulkHandler(selectedItem.id));
				} else {
					acfSuccessHandlers &&
						(await acfSuccessHandlers.addBulkHandler(selectedItem.id));
				}
			}
			generatedRecords.length > 0 &&
				generatedRecords.forEach((record) => {
					queryClient.refetchQueries(`row-labels-${origin}-${record}`);
				});
			queryClient.refetchQueries({ queryKey: ['filtered-labels', origin] });
		}
	}

	async function handleStateChange(state): Promise<void> {
		const { isOpen } = state;

		if (isOpen !== undefined) {
			setIsDropdownOpen(isOpen);
		}
	}

	function renderOption(option): JSX.Element {
		if (isLoading) {
			return (
				<Styled styles={styles.overlay}>
					<Skeleton />
				</Styled>
			);
		}

		if (!isLoading && search.length > 0 && dLabels.length === 0) {
			return (
				<Styled styles={styles.overlay}>
					<Text>{t('global:no-results')}</Text>
				</Styled>
			);
		}

		// Check if this is a business goal and we're in AUTOMATIONS context
		const isBusinessGoal = Number(option['isInitiative']) === 1;
		const isAutomationOrigin = origin === AUTOMATIONS;

		// Find if any business goal is already selected
		const selectedBusinessGoal = sLabels.find(
			(label) => Number(label.isInitiative) === 1
		);

		// Determine if this option should be disabled
		const shouldDisable =
			isBusinessGoal &&
			isAutomationOrigin &&
			selectedBusinessGoal &&
			Number(selectedBusinessGoal.id) !== Number(option.id);

		// Basic label, no initiatives
		if (!hasInitiatives) {
			return <span data-testid="label-option">{option['label']}</span>;
		}

		// Business goal rendering
		if (isBusinessGoal) {
			return (
				<TooltipWrapper
					shouldDisplay={shouldDisable}
					// content={'Automations can only be assigned to one Business Goal'}
					content={t(
						'"generative-marketing:labels-dropdown:automation-business-goal-tooltip'
					)}
					placement="right"
				>
					<Flex
						data-testid={`business-goal-${option.id}`}
						alignItems="center"
						pt="1px"
						style={shouldDisable ? { opacity: 0.5, cursor: 'not-allowed' } : {}}
					>
						<Icon
							use={goalTrophy}
							fill={
								shouldDisable
									? PrimitiveColors['midnight']['700']
									: PrimitiveColors['ac-blue']['700']
							}
							decorative
							size="small"
							mr="sp300"
						/>
						<span
							style={
								shouldDisable
									? { color: PrimitiveColors['midnight']['700'] }
									: {}
							}
						>
							{option['label']}
						</span>
					</Flex>
				</TooltipWrapper>
			);
		}

		// Regular label
		return <span data-testid="label-option">{option['label']}</span>;
	}

	/**
	 * Debounces the setSearchValue function to delay its execution by 500 milliseconds.
	 * This is useful for handling search input changes and preventing unnecessary API calls.
	 * @param {Function} setSearchValue - The function to be debounced.
	 * @param {number} delay - The delay in milliseconds before executing the debounced function.
	 */
	// eslint-disable-next-line react-hooks/exhaustive-deps
	const debouncedSetSearchValue = useCallback(
		debounce(
			() =>
				setParams((prev) => {
					return {
						...prev,
						search,
					};
				}),
			500
		),
		[search]
	);

	/**
	 * Handles the search event.
	 *
	 * @param e - The search query.
	 */
	function handleSearch(e: string): void {
		setSearch(e);
	}

	useEffect(() => {
		debouncedSetSearchValue();
	}, [search, debouncedSetSearchValue]);

	return (
		<>
			<DropdownMultiselect
				controlSearch
				defaultOptionRenderer={(option): JSX.Element => renderOption(option)}
				isSearchable
				maxMenuHeight="20rem"
				maxMenuWidth="13.5rem"
				minMenuWidth="13.5rem"
				searchValue={search}
				onSearch={handleSearch}
				// this is a required prop but since we've taken over search it does nothing.
				noSearchResultsMessage={t('global:no-results')}
				onSelect={handleSelect}
				options={
					isLoading
						? // necessary placeholder data to populate skeleton loader
						  [
								{ label: t('global:loading') },
								{ label: t('global:loading') },
								{ label: t('global:loading') },
						  ]
						: hasInitiatives
						? generatedDisplayedLabels
						: dLabels
				}
				optionToString={(option): string => option['label']}
				placeholder={t('generative-marketing:labels-dropdown:placeholder')}
				popoverTestId={`multiselect-popover-${recordId}`}
				renderMenuActions={(): JSX.Element => <ModalTrigger />}
				renderTrigger={
					utilizeRenderTrigger
						? (): JSX.Element => (
								<DropdownTrigger hasInitiatives={hasInitiatives} />
						  )
						: undefined
				}
				searchPlaceholder={t('global:search')}
				selected={sLabels.sort(
					(a, b) =>
						new Date(a.createdTimestamp).getTime() -
						new Date(b.createdTimestamp).getTime()
				)}
				onStateChange={handleStateChange}
				triggerTestId={LABELS_CHIP_DROPDOWN_TRIGGER_TEST_ID}
			/>
		</>
	);
};
