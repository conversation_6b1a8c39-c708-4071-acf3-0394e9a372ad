import React, { useCallback, useEffect, useState } from 'react';
import { AxiosResponse } from 'axios';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import Flex from '@activecampaign/camp-components-flex';
import Styled from '@activecampaign/camp-components-styled';
import { useTranslation } from '@activecampaign/core-translations-client';
import QueryProvider from '@src/components/QueryProvider';
import * as Api from '@src/api/notes';
import DebouncedSideEffect from '@src/components/DebouncedSideEffect';
import DraftForm from '@src/components/DraftForm';
import { NOTES } from '@src/constants/queries';
import { NotesRelType, ResourceType, DynamicObj } from '@src/types';
import { translateResourceToRelType } from '@src/utils';

import useNoteEditor, { NEW_NOTE } from './hooks/useNoteEditor';
import styles from './NotesContent.styles';

export type Props = {
	onRequestSave: () => void;
	onDisplayAlert: (type: string, message: string) => void;
	onRequestCancel: (
		handler?: Function,
		callback?: Function,
		closeDrawer?: boolean
	) => void;
	resourceId: string;
	resourceType: ResourceType;
	newNoteTarget?: string;
};

const NotesDraft: React.FC<Props> = ({
	onDisplayAlert,
	onRequestCancel,
	resourceId,
	resourceType,
	newNoteTarget,
	onRequestSave,
}: Props): React.ReactElement => {
	const { t } = useTranslation();
	const queryClient = useQueryClient();
	const [newCachedTarget, setNewCachedTarget] = useState('');
	const [newResourceType, newResourceID] = newNoteTarget?.split('-') || [];

	const {
		closeEditor,
		editor,
		newNote,
		resetNote,
		setDraftNote,
		setSaveDraft,
		setSavePublished,
		updateText,
	} = useNoteEditor();
	const recordResourceType = newResourceType || resourceType;
	const recordResouceId = newResourceID || resourceId;
	// Notes created via the "Add Note" draft section need to be created
	// before further updates can be made
	const createNote = useMutation(async (id: string): Promise<void> => {
		const draft = editor[id];

		setSaveDraft(id, true);

		const response = await Api.createNote({
			isDraft: true,
			isNew: false,
			note: draft.text,
			relationType: translateResourceToRelType(
				recordResourceType,
				'note'
			) as NotesRelType,
			relationId: recordResouceId,
		});

		setSaveDraft(id, false);
		setDraftNote(id, response.data.note);
	});

	const saveNoteAsDraft = useMutation(async (id: string): Promise<void> => {
		const draft = editor[id];

		setSaveDraft(id, true);
		// In this case the "id" being passed might sometimes be "NEW_NOTE" if the user
		// is updating a yet-unpublished note version. The attached .note instance contains
		// the actual ID needed, as no note with the ID "NEW_NOTE" exists in the database
		await Api.updateNote(draft.note.id, {
			isDraft: true,
			note: draft.text,
			isNew: false,
			relationType: translateResourceToRelType(recordResourceType, 'note'),
			relationId: recordResouceId,
		});

		setSaveDraft(id, false);

		if (id !== NEW_NOTE) {
			queryClient.invalidateQueries({
				queryKey: [NOTES, recordResourceType, recordResouceId],
			});
		}
	});

	const saveNoteAsPublished = useMutation(async (id: string): Promise<void> => {
		const draft = editor[id];

		setSavePublished(id, true);

		await Api.updateNote(draft.note.id, {
			isDraft: false,
			isNew: true,
			note: draft.text,
			relationType: translateResourceToRelType(recordResourceType, 'note'),
			relationId: recordResouceId,
		});

		if (id === NEW_NOTE) {
			resetNote(NEW_NOTE);
			setSavePublished(id, false);
			onDisplayAlert('success', t('notes-panel.note-added-success'));
		} else {
			closeEditor(id);
			onDisplayAlert('success', t('notes-panel.note-updated-success'));
		}
		if (onRequestSave) {
			onRequestSave();
		}
		queryClient.invalidateQueries({
			queryKey: [NOTES, recordResourceType, recordResouceId],
		});
	});

	/*
	 * ACTION HANDLERS
	 */

	// Until a global React handler is provided, canceling and deleting notes
	// open a modal within the Ember app which, if confirmed, will call the
	// appropriate API handler and success/error callback as provided below
	const handleCancelNewNote = (): void => {
		if (!newNote.note && !newNote.text) {
			// If there is no new note just close the editor
			onRequestCancel(null, null, true);
		} else {
			const handler = async (): Promise<AxiosResponse<DynamicObj>> => {
				return Api.deleteNote(newNote.note.id);
			};

			const callback = (err: DynamicObj): void => {
				if (!err) {
					resetNote(NEW_NOTE);
					onRequestCancel(null, null, true);
				}
			};

			onRequestCancel(handler, callback);
		}
	};

	// A newly added note must first be created before updates can be made
	const handleSaveNewNote = useCallback((): void => {
		if (newNoteTarget !== newCachedTarget) {
			if (newNote?.note?.id) {
				Api.deleteNote(newNote.note.id);
			}
			createNote.mutate(NEW_NOTE);
			setNewCachedTarget(newNoteTarget);
		}

		if (newNote.note && newNoteTarget === newCachedTarget) {
			saveNoteAsDraft.mutate(NEW_NOTE);
		} else if (newNoteTarget === newCachedTarget) {
			createNote.mutate(NEW_NOTE);
		}
	}, [
		createNote,
		newCachedTarget,
		newNote.note,
		newNoteTarget,
		saveNoteAsDraft,
	]);

	const draggableStyles = {
		textarea: {
			resize: 'none',
		},
		borderBottom: 'none',
		padding: '0',
	};

	const textAreaStyles = {
		...styles.draft,
		...draggableStyles,
	};

	useEffect(() => {
		if (newNoteTarget !== newCachedTarget) {
			handleSaveNewNote();
		}
	}, [handleSaveNewNote, newCachedTarget, newNoteTarget]);

	return (
		<Flex
			direction="column"
			id="tab-notes"
			role="tabpanel"
			styles={styles.root}
		>
			<Styled styles={textAreaStyles}>
				<DebouncedSideEffect
					action={(text: string): void => updateText(NEW_NOTE, text)}
					sideEffect={handleSaveNewNote}
				>
					{(handleUpdateText): React.ReactElement => (
						<DraftForm
							cancelButtonText={t('notes-panel.cancel')}
							isPublishDisabled={!newNote.note}
							isSavingDraft={newNote.isSavingDraft}
							isSavingPublished={newNote.isSavingPublished}
							onClickCancel={handleCancelNewNote}
							onClickPublish={(): void => saveNoteAsPublished.mutate(NEW_NOTE)}
							onUpdateText={handleUpdateText}
							publishButtonText={t('notes-panel.save')}
							text={newNote.text}
							isNotesV2Enabled
						/>
					)}
				</DebouncedSideEffect>
			</Styled>
		</Flex>
	);
};

const WithProvider = (props: Props): React.ReactElement => {
	return (
		<QueryProvider>
			<NotesDraft {...props} />
		</QueryProvider>
	);
};

export default WithProvider;
