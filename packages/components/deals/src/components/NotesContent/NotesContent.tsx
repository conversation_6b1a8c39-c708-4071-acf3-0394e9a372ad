import React, { useCallback, useState, useEffect } from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import {
	useInfiniteQuery,
	useMutation,
	useQueryClient,
} from '@tanstack/react-query';
import { AxiosResponse } from 'axios';

import Flex from '@activecampaign/camp-components-flex';
import { Note as NoteIcon } from '@activecampaign/camp-components-icon';
import Styled from '@activecampaign/camp-components-styled';
import Text from '@activecampaign/camp-components-text';
import { useTranslation } from '@activecampaign/core-translations-client';
import { User } from '@src/types';
import * as Api from '@src/api/notes';
import DebouncedSideEffect from '@src/components/DebouncedSideEffect';
import DraftForm from '@src/components/DraftForm';
import Loading from '@src/components/Loading';
import PanelActions from '@src/components/PanelActions';
import { MemoizedNoteTile as NoteTile } from '@src/components/NoteTile';
import { NOTES } from '@src/constants/queries';
import {
	AssociatedNote,
	Note,
	NotesRelType,
	ResourceType,
	DynamicObj,
} from '@src/types';
import { translateResourceToRelType } from '@src/utils';

import useNoteEditor, { NEW_NOTE } from './hooks/useNoteEditor';
import { fetchNotes, getNextPageParam, getTotalNotes } from './utils';
import { ResourceTypes } from '@src/constants';
import styles from './NotesContent.styles';

export type Props = {
	currentUser: User;
	onDisplayAlert: (type: string, message: string) => void;
	onHandleRefresh?: VoidFunction;
	onRequestCancel: (handler: Function, callback: Function) => void;
	onRequestDelete: (handler: Function, callback: Function) => void;
	onSetTotalNotes: (total: number) => void;
	acctTimeZone: string;
	dateTimeFormat: string;
	resourceId: string;
	resourceType: ResourceType;
	shouldRefresh?: boolean;
	onRequestOpenNoteDrawer: VoidFunction;
};

const NotesContent: React.FC<Props> = ({
	onDisplayAlert,
	onHandleRefresh,
	onRequestCancel,
	onRequestDelete,
	resourceId,
	resourceType,
	acctTimeZone,
	dateTimeFormat,
	shouldRefresh = false,
	onSetTotalNotes,
	onRequestOpenNoteDrawer,
	currentUser,
}: Props): React.ReactElement => {
	const { t } = useTranslation();
	const queryClient = useQueryClient();

	const isContactAndDealsOnetoOneEnabled =
		resourceType !== ResourceTypes.Account;
	const isOneToOneEnabled =
		resourceType === ResourceTypes.Account || isContactAndDealsOnetoOneEnabled;
	const [includeRelatedNotes, setIncludeRelatedNotes] = useState(true);
	const [isNewNoteVisible, setIsNewNoteVisible] = useState(false);

	const {
		closeEditor,
		editNote,
		editor,
		newNote,
		resetNote,
		resetNoteEditor,
		setDraftNote,
		setSaveDraft,
		setSavePublished,
		updateText,
	} = useNoteEditor();

	useEffect(() => {
		if (shouldRefresh) {
			resetNoteEditor();
			onHandleRefresh();
		}
	}, [onHandleRefresh, resetNoteEditor, shouldRefresh]);

	/*
	 * DATA HANDLERS
	 */

	const notes = useInfiniteQuery(
		[NOTES, resourceType, resourceId, includeRelatedNotes],
		({ queryKey, pageParam }) => fetchNotes({ queryKey, pageParam }),
		{
			getNextPageParam,
			onSuccess: (data) => onSetTotalNotes(+data.pages[0].meta.total),
		}
	);

	// Notes created via the "Add Note" draft section need to be created
	// before further updates can be made
	const createNote = useMutation(async (id: string): Promise<void> => {
		const draft = editor[id];

		setSaveDraft(id, true);
		const response = await Api.createNote({
			isDraft: true,
			isNew: false,
			note: draft.text,
			relationType: translateResourceToRelType(
				resourceType,
				'note'
			) as NotesRelType,
			relationId: resourceId,
		});

		setSaveDraft(id, false);
		setDraftNote(id, response.data.note);
	});

	const saveNoteAsDraft = useMutation(async (id: string): Promise<void> => {
		const draft = editor[id];

		setSaveDraft(id, true);
		// In this case the "id" being passed might sometimes be "NEW_NOTE" if the user
		// is updating a yet-unpublished note version. The attached .note instance contains
		// the actual ID needed, as no note with the ID "NEW_NOTE" exists in the database
		await Api.updateNote(draft.note.id, {
			isDraft: true,
			isNew: false,
			note: draft.text,
			relationType: translateResourceToRelType(resourceType, 'note'),
			relationId: resourceId,
		});

		setSaveDraft(id, false);

		if (id !== NEW_NOTE) {
			queryClient.invalidateQueries({
				queryKey: [NOTES, resourceType, resourceId],
			});
		}
	});

	const saveNoteAsPublished = useMutation(async (id: string): Promise<void> => {
		const draft = editor[id];
		setSavePublished(id, true);

		await Api.updateNote(draft.note.id, {
			isDraft: false,
			isNew: true,
			note: draft.text,
			relationType: translateResourceToRelType(resourceType, 'note'),
			relationId: resourceId,
		});

		if (id === NEW_NOTE) {
			resetNote(NEW_NOTE);
			setIsNewNoteVisible(false);
			setSavePublished(id, false);
			onDisplayAlert('success', t('notes-panel.note-added-success'));
		} else {
			closeEditor(id);
			onDisplayAlert('success', t('notes-panel.note-updated-success'));
		}

		queryClient.invalidateQueries({
			queryKey: [NOTES, resourceType, resourceId],
		});
	});

	/*
	 * ACTION HANDLERS
	 */

	// Until a global React handler is provided, canceling and deleting notes
	// open a modal within the Ember app which, if confirmed, will call the
	// appropriate API handler and success/error callback as provided below
	const handleCancelNewNote = (): void => {
		if (!newNote.note && !newNote.text) {
			// If there is no new note just close the editor
			setIsNewNoteVisible(false);
		} else {
			const handler = async (): Promise<AxiosResponse<DynamicObj>> => {
				return Api.deleteNote(newNote.note.id);
			};

			const callback = (err: DynamicObj): void => {
				if (!err) {
					resetNote(NEW_NOTE);
					setIsNewNoteVisible(false);
				}
			};

			onRequestCancel(handler, callback);
		}
	};

	const handleCancelEditNote = (note: Note): void => {
		const draft = editor[note.id];

		const handler = async (): Promise<AxiosResponse<DynamicObj>> => {
			return Api.updateNote(note.id, {
				isDraft: draft.note.is_draft,
				note: draft.note.note,
			});
		};

		const callback = (err: DynamicObj): void => {
			if (err) {
				onDisplayAlert('error', t('notes-panel.note-updated-error'));
			} else {
				closeEditor(note.id);

				queryClient.invalidateQueries({
					queryKey: ['notes', resourceType, resourceId],
				});
			}
		};

		onRequestCancel(handler, callback);
	};

	const handleDeleteNote = useCallback(
		(note: Note): void => {
			const handler = async (): Promise<AxiosResponse<DynamicObj>> => {
				return Api.deleteNote(note.id);
			};

			const callback = (err: DynamicObj): void => {
				if (err) {
					onDisplayAlert('error', t('notes-panel.note-deleted-error'));
				} else {
					onDisplayAlert('success', t('notes-panel.note-deleted-success'));

					// TODO: Maybe just find individual note and remove?
					queryClient.invalidateQueries({
						queryKey: ['notes', resourceType, resourceId],
					});
				}
			};

			onRequestDelete(handler, callback);
		},
		[onDisplayAlert, onRequestDelete, queryClient, resourceId, resourceType, t]
	);

	// A newly added note must first be created before updates can be made
	const handleSaveNewNote = (): void => {
		if (newNote.note) {
			saveNoteAsDraft.mutate(NEW_NOTE);
		} else {
			createNote.mutate(NEW_NOTE);
		}
	};

	const handleScroll = (): void => {
		if (notes.hasNextPage) {
			notes.fetchNextPage();
		}
	};

	const handleOnClickAction = (): void => {
		if (isOneToOneEnabled && onRequestOpenNoteDrawer) {
			resetNoteEditor();
			onRequestOpenNoteDrawer();
			return;
		}
		setIsNewNoteVisible(true);
	};

	const renderContent = ((): React.ReactElement => {
		if (
			!notes.hasNextPage &&
			!notes.isFetching &&
			!getTotalNotes(notes.data?.pages)
		) {
			return (
				<Flex
					alignItems="center"
					data-testid="empty-state"
					justifyContent="center"
					styles={styles.emptyState}
				>
					<Flex
						direction="column"
						alignItems="center"
						styles={styles.emptyWrapper}
					>
						<Styled styles={styles.icon}>
							<NoteIcon decorative size="large" />
						</Styled>

						<Styled>
							<Text color="slate300" family="ffStandard" size="fs200">
								{t('notes-panel.empty-state')}
							</Text>
						</Styled>
					</Flex>
				</Flex>
			);
		}

		return (
			<Styled data-testid="notes-scroll-container" styles={styles.scroll}>
				<InfiniteScroll
					hasMore
					initialLoad
					loadMore={handleScroll}
					useWindow={false}
				>
					<Styled role="list">
						{notes.data?.pages.map((page, index) => (
							<React.Fragment key={index}>
								{page.notes.map((note: AssociatedNote) => (
									<Styled key={note.id} role="listitem" styles={styles.tile}>
										<NoteTile
											currentUser={currentUser}
											acctTimeZone={acctTimeZone}
											dateTimeFormat={dateTimeFormat}
											draft={editor[note.id]}
											onClickCancel={handleCancelEditNote}
											onClickDelete={handleDeleteNote}
											onClickEdit={editNote}
											onSaveDraft={(note: Note): void =>
												saveNoteAsDraft.mutate(note.id)
											}
											onSaveNote={(note: Note): void =>
												saveNoteAsPublished.mutate(note.id)
											}
											onSetNoteText={(note: Note, text: string): void =>
												updateText(note.id, text)
											}
											note={note}
										/>
									</Styled>
								))}
							</React.Fragment>
						))}
					</Styled>

					{notes.isFetching && <Loading />}
				</InfiniteScroll>
			</Styled>
		);
	})();

	return (
		<Flex
			direction="column"
			id="tab-notes"
			role="tabpanel"
			styles={styles.root}
		>
			<PanelActions
				actionText={t('notes-panel.add-note')}
				onClickAction={handleOnClickAction}
				onClickToggle={(): void => setIncludeRelatedNotes((prev) => !prev)}
				toggleText={t(
					`notes-panel.${includeRelatedNotes ? 'hide' : 'show'}-related`
				)}
				hideToggleText={
					resourceType === ResourceTypes.Contact ||
					resourceType === ResourceTypes.Deal
				}
			/>

			{isNewNoteVisible && (
				<Styled styles={styles.draft}>
					<DebouncedSideEffect
						action={(text: string): void => updateText(NEW_NOTE, text)}
						sideEffect={handleSaveNewNote}
					>
						{(handleUpdateText): React.ReactElement => (
							<DraftForm
								cancelButtonText={t('notes-panel.cancel')}
								isPublishDisabled={!newNote.note}
								isSavingDraft={newNote.isSavingDraft}
								isSavingPublished={newNote.isSavingPublished}
								onClickCancel={handleCancelNewNote}
								onClickPublish={(): void =>
									saveNoteAsPublished.mutate(NEW_NOTE)
								}
								onUpdateText={handleUpdateText}
								publishButtonText={t('notes-panel.save')}
								text={newNote.text}
							/>
						)}
					</DebouncedSideEffect>
				</Styled>
			)}

			{renderContent}
		</Flex>
	);
};

export default NotesContent;
