import React, { useRef, useState } from 'react';
import { useFormikContext } from 'formik';
import Styled from '@activecampaign/camp-components-styled';
import Dropdown from '@activecampaign/camp-components-dropdown';
import Input from '@activecampaign/camp-components-input';
import Radio from '@activecampaign/camp-components-radio';
import Flex from '@activecampaign/camp-components-flex';
import Text from '@activecampaign/camp-components-text';
import Button from '@activecampaign/camp-components-button';
import FocusTrap from 'focus-trap-react';
import styled from '@activecampaign/camp-core-styled';
import styles from './fields.styles';
import { useQueryClient } from '@activecampaign/platform-core-queries';
import { useTranslation } from '@activecampaign/core-translations-client';
import { intersectionWith, isEqual, xorWith } from 'lodash';
import {
	ErrorBanner,
	RadioLabel,
	SelectedProducts,
	GenerateCodeLabel,
	BuyXGetY,
} from '@src/components';
import {
	Categories,
	CatOption,
	DropdownOption,
	FormValues,
	StoreOption,
} from '@src/ecomm-stripo-coupon-block.types';
import {
	SPEC_CAT_WOO,
	SPEC_CAT_SHOPIFY,
	ALL_CAT_SHOPIFY,
	FREE_SHIPPING,
	SERVICE_SHOPIFY,
	ALL_CAT_WOO,
	ALL_PRODUCTS,
	AUTOMATION_COUPON,
	BUY_X_GET_Y,
	COUPON_EXPIRATION_DATE,
	COUPON_EXPIRATION_NEVER,
	COUPON_EXPIRATION_SPEC_NUM,
	COUPON_EXPIRATION_TIME,
	DEFAULT_VALS,
	EXPIRATION_TIME_OPTIONS,
	FIXED_AMOUNT,
	PAGE_PRODUCT_SELECT,
	PERCENTAGE,
	SERVICE_WOOCOMMERCE,
	SHOPIFY_DISCOUNT_OPTIONS,
	SPEC_PRODUCTS,
	STATIC_COUPON,
	WOO_DISCOUNT_OPTIONS,
} from '@src/ecomm-stripo-coupon-block.constants';
import { format, isValid, parse } from 'date-fns';
import { CalendarEvent } from '@activecampaign/camp-components-icon';
import { DatePicker } from '@activecampaign/camp-components-date-picker';
import { usePopper } from 'react-popper';
import { Portal } from '@reach/portal';
import { isLocalDevelopment } from '@src/utils';

type FieldsProps = {
	categories: Categories;
	couponType: typeof STATIC_COUPON | typeof AUTOMATION_COUPON;
	pageChange: (page, value) => void;
	saveProductChanges: (products) => void;
	stores: Array<StoreOption>;
};

export const Fields: React.FC<FieldsProps> = ({
	categories,
	couponType,
	pageChange,
	saveProductChanges,
	stores,
}) => {
	const { t } = useTranslation();
	const queryClient = useQueryClient();
	const { setFieldValue, setValues, values, errors, touched } =
		useFormikContext<FormValues>();
	const [isDatePopperOpen, setIsDatePopperOpen] = useState<boolean>(false);
	const [selectedDate, setSelectedDate] = useState<Date>();
	const popperCalRef = useRef<HTMLDivElement>(null);
	const buttonCalRef = useRef<HTMLButtonElement>(null);
	const [popperElement, setPopperElement] = useState<HTMLDivElement | null>(
		null
	);
	const calPopper = usePopper(popperCalRef.current, popperElement, {
		placement: 'bottom-start',
	});
	const CalendarButtonStyled = styled('button')({
		background: 'none',
		border: 'none',
		cursor: 'pointer',
	});

	const closeDatePopper = (): void => {
		setIsDatePopperOpen(false);
		buttonCalRef?.current?.focus();
	};

	const storeCategoryOptions = categories.groups.map((cat) => ({
		value: cat.id,
		label: cat.title,
	}));

	const storeSelectedCategoryOptions = intersectionWith(
		storeCategoryOptions,
		values.specificCategory,
		isEqual
	);

	const defaultVals = DEFAULT_VALS(values.store?.service);
	const APPLY_TO_OPTIONS = [
		values.store?.service === SERVICE_WOOCOMMERCE
			? ALL_CAT_WOO
			: ALL_CAT_SHOPIFY,
		values.store?.service === SERVICE_WOOCOMMERCE
			? SPEC_CAT_WOO
			: SPEC_CAT_SHOPIFY,
		ALL_PRODUCTS,
		SPEC_PRODUCTS,
	];
	const SPEC_CAT_PLACEHOLDER = t(
		values.store?.service === SERVICE_WOOCOMMERCE
			? 'ecomm-coupons:fields:specificCategory:placeholder-woo'
			: 'ecomm-coupons:fields:specificCategory:placeholder-shopify'
	);

	const handleStoreSelect = (store): void => {
		setValues({
			store,
			couponType: couponType,
			storeService: store.service,
			applyTo: defaultVals.applyTo,
			specificCategory: defaultVals.specificCategory,
			specificProducts: defaultVals.specificProducts,
			discountType: defaultVals.discountType,
			couponCode: defaultVals.couponCode,
			description: defaultVals.description,
			couponValue: defaultVals.couponValue,
			buyXGetYBuys: defaultVals.buyXGetYBuys,
			buyXGetYBuysItemQuantity: defaultVals.buyXGetYBuysItemQuantity,
			buyXGetYBuysItemType: defaultVals.buyXGetYBuysItemType,
			expiration: defaultVals.expiration,
			expirationNum: defaultVals.expirationNum,
			expirationTimeAmount: defaultVals.expirationTimeAmount,
			expirationTimeType: defaultVals.expirationTimeType,
			expirationDate: defaultVals.expirationDate,
			buyXGetYBuysItemProducts: defaultVals.buyXGetYBuysItemProducts,
			buyXGetYBuysItemCategory: defaultVals.buyXGetYBuysItemCategory,
			buyXGetYGetQuantity: defaultVals.buyXGetYGetQuantity,
			buyXGetYGetType: defaultVals.buyXGetYGetType,
			buyXGetYBuysMoneyMin: defaultVals.buyXGetYGetQuantity,
			buyXGetYGetProducts: defaultVals.buyXGetYGetProducts,
			buyXGetYGetCategory: defaultVals.buyXGetYGetCategory,
		});
	};

	const handleTypeSelect = (val): void => {
		setValues({
			store: values.store,
			couponType: couponType,
			storeService: values.storeService,
			applyTo: defaultVals.applyTo,
			specificCategory: defaultVals.specificCategory,
			specificProducts: defaultVals.specificProducts,
			discountType: val,
			couponCode: defaultVals.couponCode,
			description: defaultVals.description,
			couponValue: defaultVals.couponValue,
			buyXGetYBuys: defaultVals.buyXGetYBuys,
			buyXGetYBuysItemQuantity: defaultVals.buyXGetYBuysItemQuantity,
			buyXGetYBuysItemType: defaultVals.buyXGetYBuysItemType,
			expiration: defaultVals.expiration,
			expirationNum: defaultVals.expirationNum,
			expirationTimeAmount: defaultVals.expirationTimeAmount,
			expirationTimeType: defaultVals.expirationTimeType,
			expirationDate: defaultVals.expirationDate,
			buyXGetYBuysItemProducts: defaultVals.buyXGetYBuysItemProducts,
			buyXGetYBuysItemCategory: defaultVals.buyXGetYBuysItemCategory,
			buyXGetYGetQuantity: defaultVals.buyXGetYGetQuantity,
			buyXGetYBuysMoneyMin: defaultVals.buyXGetYGetQuantity,
			buyXGetYGetType: defaultVals.buyXGetYGetType,
			buyXGetYGetProducts: defaultVals.buyXGetYGetProducts,
			buyXGetYGetCategory: defaultVals.buyXGetYGetCategory,
		});
	};

	const handleCouponValuePercChange = (e): void => {
		if (parseInt(e.target.value)) {
			if (parseInt(e.target.value) > 100) {
				setFieldValue('couponValue', parseInt('100'));
			} else {
				setFieldValue('couponValue', parseInt(e.target.value));
			}
		} else {
			setFieldValue('couponValue', '');
		}
	};

	const handleDateInputChange = (e): void => {
		setFieldValue('expirationDate', e.currentTarget.value);
		const date = parse(e.currentTarget.value, 'MM/dd/yy', new Date());
		if (isValid(date)) {
			setSelectedDate(date);
		} else {
			setSelectedDate(undefined);
		}
	};

	const handleCodeKeyPress = (e): void => {
		if ([13, 32].includes(e.which)) {
			e.preventDefault();
		}
	};

	const handleInputKeyPress = (e): void => {
		if ([13].includes(e.which)) {
			e.preventDefault();
		}
	};

	const handleDaySelect = (date): void => {
		setSelectedDate(date);
		if (date) {
			setFieldValue('expirationDate', format(date, 'MM/dd/yy'));
			closeDatePopper();
		} else {
			setFieldValue('expirationDate', '');
		}
	};

	const handleDatePickerInputClick = (): void => {
		setIsDatePopperOpen(true);
	};

	const resetProductsCategories = (): void => {
		setFieldValue('specificCategory', defaultVals.specificCategory);
		setFieldValue('specificProducts', defaultVals.specificProducts);
	};

	const CalendarButton: React.FC = () => (
		<CalendarButtonStyled onClick={handleDatePickerInputClick}>
			<CalendarEvent
				size="small"
				title={t('ecomm-coupons:fields:calendar-title')}
			/>
		</CalendarButtonStyled>
	);

	const errorsLength = Object.keys(errors).length;

	return (
		<div data-testid="integrations-coupon-fields">
			{errorsLength > 0 && (
				<ErrorBanner errors={errors} service={values.store?.service} />
			)}
			<Styled mb="sp600">
				<Flex>
					<Flex.Item flex={1} mr="sp400" styles={{ width: '50%' }}>
						<Dropdown
							appearance="default"
							label={t('ecomm-coupons:fields:store:label')}
							labelPosition="top"
							selected={stores.find(
								(store) => store.value === values?.store?.value
							)}
							onSelect={handleStoreSelect}
							optionToString={(option: StoreOption): string => option.label}
							options={stores}
							disabled={isLocalDevelopment() ? true : true}
							placeholder={t('ecomm-coupons:fields:store:placeholder')}
							popoverPlacement="bottom"
							triggerTestId="choose-store-dropdown"
							showTriggerArrow
						/>
					</Flex.Item>
					<Flex.Item flex={1} styles={{ width: '50%' }}>
						<Dropdown
							appearance="default"
							label={t('ecomm-coupons:fields:discountType:label')}
							labelPosition="top"
							selected={values.discountType}
							onSelect={handleTypeSelect}
							optionToString={(option: DropdownOption): string =>
								t(option.label)
							}
							options={
								values.store?.service === SERVICE_WOOCOMMERCE
									? WOO_DISCOUNT_OPTIONS
									: SHOPIFY_DISCOUNT_OPTIONS
							}
							placeholder={t('ecomm-coupons:fields:discountType:placeholder')}
							popoverPlacement="bottom"
							triggerTestId="choose-discount-type-dropdown"
							showTriggerArrow
						/>
					</Flex.Item>
				</Flex>
			</Styled>
			{values.store?.service === SERVICE_WOOCOMMERCE && (
				<Styled mb="sp500">
					<Input
						flushed={true}
						onKeyDown={handleInputKeyPress}
						label={t('ecomm-coupons:fields:description:label')}
						placeholder={t('ecomm-coupons:fields:description:placeholder')}
						type="text"
						invalid={errors?.description && touched?.description ? true : false}
						onChange={(e): void => {
							setFieldValue('description', e.target.value);
						}}
						value={values.description}
						styles={styles.input}
					/>
				</Styled>
			)}
			<Styled mb="sp500">
				<GenerateCodeLabel />
				<Input
					flushed={true}
					onKeyDown={handleCodeKeyPress}
					placeholder="MYCOUPON1"
					id="couponCode"
					name="couponCode"
					type="text"
					helperText={t('ecomm-coupons:fields:couponCode:helper')}
					invalid={errors?.couponCode && touched?.couponCode ? true : false}
					onChange={(e): void => {
						setFieldValue('couponCode', e.target.value);
					}}
					value={values.couponCode}
					styles={styles.input}
				/>
			</Styled>
			{values.discountType === FIXED_AMOUNT && (
				<Styled mb="sp500">
					<Input
						onKeyDown={handleInputKeyPress}
						autoComplete="off"
						flushed={false}
						label={t('ecomm-coupons:fields:couponValue:label')}
						type="text"
						prefix="$"
						placeholder="0.00"
						invalid={errors?.couponValue && touched?.couponValue ? true : false}
						onChange={(e): void => {
							setFieldValue('couponValue', e.target.value);
						}}
						value={values.couponValue}
						styles={styles.input}
					/>
				</Styled>
			)}
			{values.discountType === PERCENTAGE && (
				<Styled mb="sp500">
					<Input
						onKeyDown={handleInputKeyPress}
						autoComplete="off"
						flushed={false}
						label={t('ecomm-coupons:fields:couponValue:label')}
						type="text"
						suffix="%"
						invalid={errors?.couponValue && touched?.couponValue ? true : false}
						onChange={handleCouponValuePercChange}
						value={values.couponValue}
						styles={styles.input}
					/>
				</Styled>
			)}
			{values.discountType !== BUY_X_GET_Y &&
				!(
					values.discountType === FREE_SHIPPING &&
					values.store?.service === SERVICE_SHOPIFY
				) && (
					<>
						<Styled mb="sp500">
							<Dropdown
								appearance="default"
								label={t('ecomm-coupons:fields:applyTo:label')}
								labelPosition="top"
								selected={values.applyTo}
								onSelect={(value): void => {
									setFieldValue('applyTo', value);
									resetProductsCategories();
									setFieldValue(
										'buyXGetYBuysItemProducts',
										defaultVals.buyXGetYBuysItemProducts
									);
									setFieldValue(
										'buyXGetYBuysItemCategory',
										defaultVals.buyXGetYBuysItemCategory
									);
									setFieldValue(
										'buyXGetYGetProducts',
										defaultVals.buyXGetYGetProducts
									);
									setFieldValue(
										'buyXGetYGetCategory',
										defaultVals.buyXGetYGetCategory
									);
								}}
								optionToString={(option: DropdownOption): string =>
									t(option.label)
								}
								options={APPLY_TO_OPTIONS}
								placeholder={t('ecomm-coupons:fields:applyTo:placeholder')}
								popoverPlacement="bottom"
								triggerTestId="choose-apply-to-dropdown"
								showTriggerArrow
								invalid={
									(errors?.specificProducts && touched?.specificProducts
										? true
										: false) ||
									(errors?.applyTo && touched?.applyTo ? true : false)
								}
							/>
						</Styled>
						{(values?.applyTo === SPEC_CAT_SHOPIFY ||
							values?.applyTo === SPEC_CAT_WOO) && (
							<Styled mb="sp500">
								<Dropdown
									isSearchable
									label=""
									multiselect
									selectAllNone={false}
									invalid={
										errors?.specificCategory && touched?.specificCategory
											? true
											: false
									}
									multiselectTooltipString={t('global:count-selected', {
										count: values.specificCategory.length,
									})}
									selected={storeSelectedCategoryOptions}
									onSelect={({
										selectedItem,
									}: {
										selectedItem: string;
									}): void => {
										const selected =
											(Array.isArray(storeSelectedCategoryOptions) &&
												storeSelectedCategoryOptions) ||
											[];
										setFieldValue(
											'specificCategory',
											xorWith(selected, [selectedItem], isEqual)
										);
									}}
									optionToString={(option: CatOption): string => option.label}
									options={storeCategoryOptions}
									placeholder={SPEC_CAT_PLACEHOLDER}
									popoverPlacement="bottom"
									showTriggerArrow
								/>
							</Styled>
						)}
						{values?.applyTo === SPEC_PRODUCTS && (
							<Styled mb="sp500">
								<Button.Outline
									type="button"
									styles={styles.fullBtn}
									onClick={(): void => {
										saveProductChanges(values.specificProducts);
										queryClient.invalidateQueries({
											queryKey: ['manualProducts'],
										});
										pageChange(PAGE_PRODUCT_SELECT, 'specificProducts');
									}}
								>
									{t('ecomm-coupons:fields:specificProductsBtn:label')}
								</Button.Outline>
								{!!values.specificProducts.length && (
									<SelectedProducts
										saveProductChanges={saveProductChanges}
										value="specificProducts"
										isModal={false}
									/>
								)}
							</Styled>
						)}
					</>
				)}
			{values.discountType === BUY_X_GET_Y && (
				<BuyXGetY
					catOptions={storeCategoryOptions}
					handleCouponValuePercChange={handleCouponValuePercChange}
					pageChange={pageChange}
					resetProductsCategories={resetProductsCategories}
					saveProductChanges={saveProductChanges}
					specificCatPlaceholder={SPEC_CAT_PLACEHOLDER}
				/>
			)}
			{couponType === STATIC_COUPON && (
				<Flex styles={{ textAlign: 'left', gap: '8px' }} direction="column">
					<Text.Body weight="fwMedium">
						{t('ecomm-coupons:fields:expiration:label')}
					</Text.Body>
					<Text.Body color="slate400" size="fs100">
						{t('ecomm-coupons:fields:expiration-helper')}
					</Text.Body>
					<Radio.Group
						onChange={(value): void => {
							setFieldValue('expiration', value);
							setFieldValue(
								'expirationTimeAmount',
								defaultVals.expirationTimeAmount
							);
							setFieldValue(
								'expirationTimeType',
								defaultVals.expirationTimeType
							);
							setFieldValue('expirationDate', defaultVals.expirationDate);
							setFieldValue('expirationNum', defaultVals.expirationNum);
						}}
						value={values.expiration}
						name="expiration"
					>
						<RadioLabel
							value={COUPON_EXPIRATION_TIME}
							name="expiration-time"
							label={t('ecomm-coupons:fields:expiration-time:label')}
						/>
						{values.expiration === COUPON_EXPIRATION_TIME && (
							<Styled mb="sp500" ml="sp600">
								<Input
									onKeyDown={handleInputKeyPress}
									type="text"
									placeholder={t(
										'ecomm-coupons:fields:expirationTimeAmount:placeholder'
									)}
									onChange={(e): void => {
										setFieldValue('expirationTimeAmount', e.target.value);
									}}
									value={values.expirationTimeAmount}
									invalid={
										errors?.expirationTimeAmount &&
										touched?.expirationTimeAmount
											? true
											: false
									}
									flushed="suffix"
									suffix={
										<Styled mr="sp300">
											<Dropdown
												appearance="floating"
												label=""
												labelPosition="top"
												selected={values.expirationTimeType}
												onSelect={(value): void => {
													setFieldValue('expirationTimeType', value);
												}}
												optionToString={(option: DropdownOption): string =>
													t(option.label)
												}
												options={EXPIRATION_TIME_OPTIONS}
												popoverPlacement="bottom"
												triggerTestId=""
												showTriggerArrow
											/>
										</Styled>
									}
									styles={{
										'.c-Input-text': { paddingLeft: 'sp300' },
									}}
								/>
							</Styled>
						)}
						<RadioLabel
							value={COUPON_EXPIRATION_DATE}
							name="expiration-date"
							label={t('ecomm-coupons:fields:expiration-date:label')}
						/>
						{values.expiration === COUPON_EXPIRATION_DATE && (
							<Styled mb="sp500" ml="sp600" ref={popperCalRef}>
								<Input
									onKeyDown={handleInputKeyPress}
									label=""
									placeholder={'mm/dd/yy'}
									value={values.expirationDate}
									onChange={handleDateInputChange}
									onClick={handleDatePickerInputClick}
									invalid={
										errors?.expirationDate && touched?.expirationDate
											? true
											: false
									}
									suffix={<CalendarButton />}
								/>
								{isDatePopperOpen && (
									<Portal>
										<FocusTrap
											active
											focusTrapOptions={{
												initialFocus: false,
												allowOutsideClick: true,
												clickOutsideDeactivates: true,
												onDeactivate: closeDatePopper,
											}}
										>
											<div
												tabIndex={-1}
												style={calPopper.styles.popper}
												className="dialog-sheet"
												{...calPopper.attributes.popper}
												ref={setPopperElement}
												role="dialog"
											>
												<DatePicker
													handleSetSelected={handleDaySelect}
													selectedDate={selectedDate}
													headerPrevDisabled={true}
												/>
											</div>
										</FocusTrap>
									</Portal>
								)}
							</Styled>
						)}
						<RadioLabel
							value={COUPON_EXPIRATION_SPEC_NUM}
							name="expiration-specific-number"
							label={t('ecomm-coupons:fields:expiration-number:label')}
						/>
						{values.expiration === COUPON_EXPIRATION_SPEC_NUM && (
							<Styled ml="sp600" mb="sp500">
								<Input
									onKeyDown={handleInputKeyPress}
									label=""
									placeholder={t(
										'ecomm-coupons:fields:expirationTimeAmount:placeholder'
									)}
									type="text"
									invalid={
										errors?.expirationNum && touched?.expirationNum
											? true
											: false
									}
									onChange={(e): void => {
										setFieldValue('expirationNum', e.target.value);
									}}
									value={values.expirationNum}
								/>
							</Styled>
						)}
						<RadioLabel
							value={COUPON_EXPIRATION_NEVER}
							name="expiration-never"
							label={t('ecomm-coupons:fields:expiration-never:label')}
						/>
					</Radio.Group>
				</Flex>
			)}
		</div>
	);
};
