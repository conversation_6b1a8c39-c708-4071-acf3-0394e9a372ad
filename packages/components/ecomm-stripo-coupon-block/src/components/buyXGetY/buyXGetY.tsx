import React from 'react';
import Styled from '@activecampaign/camp-components-styled';
import Text from '@activecampaign/camp-components-text';
import Radio from '@activecampaign/camp-components-radio';
import Input from '@activecampaign/camp-components-input';
import Dropdown from '@activecampaign/camp-components-dropdown';
import Flex from '@activecampaign/camp-components-flex';
import Button from '@activecampaign/camp-components-button';
import styles from './buyXGetY.styles';
import { useQueryClient } from '@activecampaign/platform-core-queries';
import { RadioLabel, SelectedProducts } from '@src/components';
import { useFormikContext } from 'formik';
import {
	FormValues,
	CatOption,
	DropdownOption,
} from '@src/ecomm-stripo-coupon-block.types';
import { useTranslation } from '@activecampaign/core-translations-client';
import { intersectionWith, isEqual, xorWith } from 'lodash';
import {
	SPEC_CAT_WOO,
	SPEC_CAT_SHOPIFY,
	BUY_X_GET_Y_BUYS_TYPE_ITEMS,
	BUY_X_GET_Y_BUYS_TYPE_MONEY,
	DEFAULT_VALS,
	PAGE_PRODUCT_SELECT,
	SERVICE_WOOCOMMERCE,
	SPEC_PRODUCTS,
} from '@src/ecomm-stripo-coupon-block.constants';

type BuyXGetYProps = {
	catOptions?: Array<CatOption>;
	handleCouponValuePercChange?: Function;
	pageChange?: (page, value) => void;
	resetProductsCategories?: Function;
	saveProductChanges?: (products) => void;
	specificCatPlaceholder?: string;
};

export const BuyXGetY: React.FC<BuyXGetYProps> = ({
	catOptions,
	handleCouponValuePercChange,
	pageChange,
	resetProductsCategories,
	saveProductChanges,
	specificCatPlaceholder,
}) => {
	const { t } = useTranslation();
	const queryClient = useQueryClient();
	const { setFieldValue, values, errors, touched } =
		useFormikContext<FormValues>();
	const defaultVals = DEFAULT_VALS(values.store?.service);

	const BUY_X_GET_Y_APPLY_TO_OPTIONS = [
		values.store?.service === SERVICE_WOOCOMMERCE
			? SPEC_CAT_WOO
			: SPEC_CAT_SHOPIFY,
		SPEC_PRODUCTS,
	];

	const handleInputKeyPress = (e): void => {
		if ([13].includes(e.which)) {
			e.preventDefault();
		}
	};

	const selectedBuyXGetYBuysItemCategoryOptions = intersectionWith(
		catOptions,
		values.buyXGetYBuysItemCategory,
		isEqual
	);

	const selectedBuyXGetYGetCategoryOptions = intersectionWith(
		catOptions,
		values.buyXGetYGetCategory,
		isEqual
	);

	return (
		<>
			<Flex
				data-testid="buyx-gety-buys"
				styles={{ textAlign: 'left', gap: '8px' }}
				direction="column"
			>
				<Text.Body weight="fwMedium">
					{t('ecomm-coupons:fields:buyXGetYBuys:label')}
				</Text.Body>
				<Styled mb="sp500">
					<Radio.Group
						onChange={(value): void => {
							setFieldValue('buyXGetYBuys', value);
						}}
						value={values.buyXGetYBuys}
						name="buyXGetYBuys"
					>
						<RadioLabel
							value={BUY_X_GET_Y_BUYS_TYPE_ITEMS}
							name="buyXGetYBuys-items"
							label={t('ecomm-coupons:fields:buyXGetYBuys:items:label')}
						/>
						{values.buyXGetYBuys === BUY_X_GET_Y_BUYS_TYPE_ITEMS && (
							<Styled mb="sp500" ml="sp600" data-testid="buyx-gety-buys-items">
								<Flex>
									<Flex.Item flex={1} mr="sp400" styles={{ width: '50%' }}>
										<Input
											data-testid="buyx-gety-buys-item-quantity-input"
											onKeyDown={handleInputKeyPress}
											autoComplete="off"
											flushed={false}
											label={t(
												'ecomm-coupons:fields:buyXGetYBuysItemQuantity:label'
											)}
											type="text"
											placeholder={t(
												'ecomm-coupons:fields:buyXGetYBuysItemQuantity:placeholder'
											)}
											invalid={
												errors?.buyXGetYBuysItemQuantity &&
												touched?.buyXGetYBuysItemQuantity
													? true
													: false
											}
											onChange={(e): void => {
												setFieldValue(
													'buyXGetYBuysItemQuantity',
													e.target.value
												);
											}}
											value={values.buyXGetYBuysItemQuantity}
											styles={styles.input}
										/>
									</Flex.Item>
									<Flex.Item flex={1} styles={{ width: '50%' }}>
										<Dropdown
											appearance="default"
											label={t(
												'ecomm-coupons:fields:buyXGetYBuysItemType:label'
											)}
											labelPosition="top"
											selected={values.buyXGetYBuysItemType}
											onSelect={(value): void => {
												setFieldValue('buyXGetYBuysItemType', value);
												resetProductsCategories();
												setFieldValue(
													'buyXGetYBuysItemProducts',
													defaultVals.buyXGetYBuysItemProducts
												);
												setFieldValue(
													'buyXGetYBuysItemCategory',
													defaultVals.buyXGetYBuysItemCategory
												);
											}}
											optionToString={(option: DropdownOption): string =>
												t(option.label)
											}
											options={BUY_X_GET_Y_APPLY_TO_OPTIONS}
											placeholder={t(
												'ecomm-coupons:fields:applyTo:placeholder'
											)}
											popoverPlacement="bottom"
											showTriggerArrow
											invalid={
												(errors?.buyXGetYBuysItemType &&
												touched?.buyXGetYBuysItemType
													? true
													: false) ||
												(errors?.buyXGetYBuysItemProducts &&
												touched?.buyXGetYBuysItemProducts
													? true
													: false)
											}
										/>
									</Flex.Item>
								</Flex>
								{(values?.buyXGetYBuysItemType === SPEC_CAT_SHOPIFY ||
									values?.applyTo === SPEC_CAT_WOO) && (
									<Styled mt="sp300">
										<Dropdown
											isSearchable
											label=""
											multiselect
											selectAllNone={false}
											multiselectTooltipString={t('global:count-selected', {
												count: values.buyXGetYBuysItemCategory.length,
											})}
											selected={selectedBuyXGetYBuysItemCategoryOptions}
											onSelect={({
												selectedItem,
											}: {
												selectedItem: string;
											}): void => {
												const selected =
													(Array.isArray(
														selectedBuyXGetYBuysItemCategoryOptions
													) &&
														selectedBuyXGetYBuysItemCategoryOptions) ||
													[];
												setFieldValue(
													'buyXGetYBuysItemCategory',
													xorWith(selected, [selectedItem], isEqual)
												);
											}}
											optionToString={(option: CatOption): string =>
												option.label
											}
											options={catOptions}
											placeholder={specificCatPlaceholder}
											popoverPlacement="bottom"
											triggerTestId="choose-categories-dropdown"
											showTriggerArrow
											invalid={
												errors?.buyXGetYBuysItemCategory &&
												touched?.buyXGetYBuysItemCategory
													? true
													: false
											}
										/>
									</Styled>
								)}
								{values?.buyXGetYBuysItemType === SPEC_PRODUCTS && (
									<Styled mt="sp300">
										<Button.Outline
											type="button"
											styles={styles.fullBtn}
											onClick={(): void => {
												saveProductChanges(values.buyXGetYBuysItemProducts);
												queryClient.invalidateQueries({
													queryKey: ['manualProducts'],
												});
												pageChange(
													PAGE_PRODUCT_SELECT,
													'buyXGetYBuysItemProducts'
												);
											}}
										>
											{t('ecomm-coupons:fields:specificProductsBtn:label')}
										</Button.Outline>
										{!!values.buyXGetYBuysItemProducts.length && (
											<SelectedProducts
												isModal={false}
												saveProductChanges={saveProductChanges}
												value="buyXGetYBuysItemProducts"
											/>
										)}
									</Styled>
								)}
							</Styled>
						)}

						<RadioLabel
							value={BUY_X_GET_Y_BUYS_TYPE_MONEY}
							name="buyXGetYBuys-money"
							label={t('ecomm-coupons:fields:buyXGetYBuys:money:label')}
						/>
						{values.buyXGetYBuys === BUY_X_GET_Y_BUYS_TYPE_MONEY && (
							<Styled mb="sp500" ml="sp600" data-testid="buyx-gety-buys-money">
								<Input
									onKeyDown={handleInputKeyPress}
									autoComplete="off"
									flushed={false}
									label=""
									type="text"
									prefix="$"
									placeholder="0.00"
									invalid={
										errors?.buyXGetYBuysMoneyMin &&
										touched?.buyXGetYBuysMoneyMin
											? true
											: false
									}
									onChange={(e): void => {
										setFieldValue('buyXGetYBuysMoneyMin', e.target.value);
									}}
									value={values.buyXGetYBuysMoneyMin}
									styles={styles.input}
								/>
							</Styled>
						)}
					</Radio.Group>
				</Styled>
			</Flex>
			<Styled data-testid="buyx-gety-then" mb="sp500">
				<Styled mb="sp300">
					<Text.Body weight="fwMedium">
						{t('ecomm-coupons:fields:buyXGetYThen:label')}
					</Text.Body>
				</Styled>
				<Styled mb="sp500">
					<Flex>
						<Flex.Item flex={1} mr="sp400" styles={{ width: '50%' }}>
							<Input
								onKeyDown={handleInputKeyPress}
								autoComplete="off"
								flushed={false}
								label={t('ecomm-coupons:fields:buyXGetYBuysItemQuantity:label')}
								type="text"
								placeholder={t(
									'ecomm-coupons:fields:buyXGetYBuysItemQuantity:placeholder'
								)}
								invalid={
									errors?.buyXGetYGetQuantity && touched?.buyXGetYGetQuantity
										? true
										: false
								}
								onChange={(e): void => {
									setFieldValue('buyXGetYGetQuantity', e.target.value);
								}}
								value={values.buyXGetYGetQuantity}
								styles={styles.input}
							/>
						</Flex.Item>
						<Flex.Item flex={1} styles={{ width: '50%' }}>
							<Dropdown
								appearance="default"
								label={t('ecomm-coupons:fields:buyXGetYBuysItemType:label')}
								labelPosition="top"
								selected={values.buyXGetYGetType}
								onSelect={(value): void => {
									setFieldValue('buyXGetYGetType', value);
									resetProductsCategories();
									setFieldValue(
										'buyXGetYGetProducts',
										defaultVals.buyXGetYGetProducts
									);
									setFieldValue(
										'buyXGetYGetCategory',
										defaultVals.buyXGetYGetCategory
									);
								}}
								optionToString={(option: DropdownOption): string =>
									t(option.label)
								}
								options={BUY_X_GET_Y_APPLY_TO_OPTIONS}
								placeholder={t('ecomm-coupons:fields:applyTo:placeholder')}
								popoverPlacement="bottom"
								showTriggerArrow
								invalid={
									(errors?.buyXGetYGetType && touched?.buyXGetYGetType
										? true
										: false) ||
									(errors?.buyXGetYGetProducts && touched?.buyXGetYGetProducts
										? true
										: false)
								}
							/>
						</Flex.Item>
					</Flex>
					{(values?.buyXGetYGetType === SPEC_CAT_SHOPIFY ||
						values?.applyTo === SPEC_CAT_WOO) && (
						<Styled mt="sp300">
							<Dropdown
								isSearchable
								label=""
								multiselect
								selectAllNone={false}
								multiselectTooltipString={t('global:count-selected', {
									count: values.buyXGetYGetCategory.length,
								})}
								selected={selectedBuyXGetYGetCategoryOptions}
								onSelect={({
									selectedItem,
								}: {
									selectedItem: string;
								}): void => {
									const selected =
										(Array.isArray(selectedBuyXGetYGetCategoryOptions) &&
											selectedBuyXGetYGetCategoryOptions) ||
										[];
									setFieldValue(
										'buyXGetYGetCategory',
										xorWith(selected, [selectedItem], isEqual)
									);
								}}
								optionToString={(option: CatOption): string => option.label}
								options={catOptions}
								placeholder={specificCatPlaceholder}
								popoverPlacement="bottom"
								triggerTestId="choose-categories-dropdown"
								showTriggerArrow
								invalid={
									errors?.buyXGetYGetCategory && touched?.buyXGetYGetCategory
										? true
										: false
								}
							/>
						</Styled>
					)}
					{values?.buyXGetYGetType === SPEC_PRODUCTS && (
						<Styled mt="sp300">
							<Button.Outline
								type="button"
								styles={styles.fullBtn}
								onClick={(): void => {
									saveProductChanges(values.buyXGetYGetProducts);
									queryClient.invalidateQueries({
										queryKey: ['manualProducts'],
									});
									pageChange(PAGE_PRODUCT_SELECT, 'buyXGetYGetProducts');
								}}
							>
								{t('ecomm-coupons:fields:specificProductsBtn:label')}
							</Button.Outline>
							{!!values.buyXGetYGetProducts.length && (
								<SelectedProducts
									isModal={false}
									saveProductChanges={saveProductChanges}
									value="buyXGetYGetProducts"
								/>
							)}
						</Styled>
					)}
				</Styled>
			</Styled>
			<Styled data-testid="buyx-gety-val" mb="sp500">
				<Input
					data-testid="buyx-gety-val-input"
					onKeyDown={handleInputKeyPress}
					autoComplete="off"
					flushed={false}
					label={t('ecomm-coupons:fields:buyXGetY:couponValue:label')}
					type="text"
					suffix="%"
					invalid={errors?.couponValue && touched?.couponValue ? true : false}
					onChange={handleCouponValuePercChange}
					value={values.couponValue}
					styles={styles.input}
				/>
			</Styled>
		</>
	);
};
