import React, { useState, useEffect } from 'react';
import Styled from '@activecampaign/camp-components-styled';
import Flex from '@activecampaign/camp-components-flex';
import Text from '@activecampaign/camp-components-text';
import LoadingIndicator from '@activecampaign/camp-components-loading-indicator';
import styles from './manualSelect.styles';
import Dropdown from '@activecampaign/camp-components-dropdown';
import Input from '@activecampaign/camp-components-input';
import Button from '@activecampaign/camp-components-button';
import { SelectedProducts, ProductCheckbox } from '@src/components';
import { useManualProducts } from '@src/hooks/use-integrations';
import { useQueryClient } from '@activecampaign/platform-core-queries';
import { xorWith, isEqual } from 'lodash';
import { useTranslation } from '@activecampaign/core-translations-client';
import {
	DropdownOption,
	Product,
	StoreOption,
	Api,
} from '@src/ecomm-stripo-coupon-block.types';
import {
	MANUAL_SELECT_INPUT_OPTIONS,
	MANUAL_SELECT_SORT_OPTIONS,
	PRODUCT_FILTER_OPERATOR_CONTAINS,
	PRODUCT_FILTER_OPERATOR_EQ,
} from '@src/ecomm-stripo-coupon-block.constants';

type ManualSelectProps = {
	api: Api;
	store: StoreOption;
	productValues: Array<Product>;
	saveChanges: (products) => void;
};

export const ManualSelect: React.FC<ManualSelectProps> = ({
	api,
	store,
	productValues,
	saveChanges,
}) => {
	const { t } = useTranslation();
	const queryClient = useQueryClient();
	const [sortBy, setSortBy] = useState<DropdownOption>(
		MANUAL_SELECT_SORT_OPTIONS[0]
	);
	const [searchType, setSearchType] = useState<DropdownOption>(
		MANUAL_SELECT_INPUT_OPTIONS[0]
	);
	const [search, setSearch] = useState<string>('');
	const [error, setError] = useState<boolean>(false);
	const [products, setProducts] = useState<Array<Product>>([]);
	const {
		data: manualProductsData,
		isError,
		isFetched,
		isLoading,
		queryParams,
		setQueryParams,
	} = useManualProducts(api, store);

	useEffect(() => {
		if (isFetched && manualProductsData?.data && !isError) {
			setProducts(manualProductsData?.data?.searchProduct);
			if (isFetched && manualProductsData?.errors) {
				setError(true);
				console.error(manualProductsData?.errors, 'errors');
			}
		}
		if (isError) {
			console.error(isError, 'isError');
		}
	}, [manualProductsData, isError, isFetched]);

	const handleSearchKeyPress = (e): void => {
		if ([13].includes(e.which)) {
			e.preventDefault();
		}
	};

	const handleProductAddRemove = (product): void => {
		console.info(product, 'product');
		console.info(productValues, 'productValues');
		const selected = (Array.isArray(productValues) && productValues) || [];
		saveChanges(xorWith(selected, [product], isEqual));
	};

	const handleSearch = (e): void => {
		const val = e.target.value;
		setSearch(val);
		if (val.length >= 3) {
			queryClient.invalidateQueries({
				queryKey: ['manualProducts'],
			});
			if (searchType.value === 'brand') {
				setQueryParams({
					...queryParams,
					offset: 0,
					search: `${searchType.value}: { filterOperator: ${PRODUCT_FILTER_OPERATOR_EQ}, value: "${val}" }`,
				});
			} else {
				setQueryParams({
					...queryParams,
					offset: 0,
					search: `${
						searchType.value
					}: { filterOperator: ${PRODUCT_FILTER_OPERATOR_CONTAINS}, value: "${val.replace(
						/\s+/g,
						'&nbsp;'
					)}" }`,
				});
			}
		} else {
			setQueryParams({
				...queryParams,
				offset: 0,
				search: '',
			});
		}
	};

	return (
		<Flex data-testid="manual-select-page" direction="column">
			<Flex
				p="sp300"
				mb="sp400"
				justifyContent="space-between"
				styles={{
					background: 'ocean100',
					borderRadius: 'radii100',
				}}
			>
				<Input
					type="text"
					placeholder={t('ecomm-coupons:manual-select-search')}
					onChange={handleSearch}
					onKeyDown={handleSearchKeyPress}
					value={search}
					flushed="suffix"
					prefixIcon="search"
					suffix={
						<Styled mr="sp300">
							<Dropdown
								appearance="floating"
								label=""
								labelPosition="top"
								selected={searchType}
								onSelect={(value: DropdownOption): void => {
									setSearchType(value);
								}}
								optionToString={(option: DropdownOption): string =>
									t(option.label)
								}
								options={MANUAL_SELECT_INPUT_OPTIONS}
								popoverPlacement="bottom"
								triggerTestId=""
								showTriggerArrow
							/>
						</Styled>
					}
					styles={{
						'.c-Input-text': { paddingLeft: 'sp300' },
					}}
				/>
				<Dropdown
					appearance="inline"
					label="Sort"
					labelPosition="left"
					selected={sortBy}
					onSelect={(value: DropdownOption): void => {
						queryClient.invalidateQueries({
							queryKey: ['manualProducts'],
						});
						setSortBy(value);
						const sortSplit = value.value.split(',');
						setQueryParams({
							...queryParams,
							offset: 0,
							sort: `${sortSplit[0]}: { sort: ${sortSplit[1]} },`,
						});
					}}
					optionToString={(option: DropdownOption): string => t(option.label)}
					options={MANUAL_SELECT_SORT_OPTIONS}
					popoverPlacement="bottom"
					triggerTestId=""
					showTriggerArrow
				/>
			</Flex>
			<Flex
				data-testid="drawer-body"
				styles={{
					...styles.drawerBody,
				}}
			>
				{isLoading && (
					<Flex justifyContent="center" my="sp1100">
						<LoadingIndicator appearance="default" size="medium" />
					</Flex>
				)}
				{!isLoading && isFetched && products?.length > 0 && (
					<Flex wrap="wrap">
						{products.map((product, i) => (
							<ProductCheckbox
								key={i}
								product={product}
								productValues={productValues}
								handleProductAddRemove={handleProductAddRemove}
							/>
						))}
					</Flex>
				)}
				{!isLoading && isFetched && products?.length === 0 && (
					<Flex justifyContent="center" my="sp1100">
						{queryParams.offset >= 20 && (
							<Text.Body as="p" weight="fwMedium">
								{t('ecomm-coupons:manual-select-search-no-more-products')}
							</Text.Body>
						)}
						{queryParams.offset === 0 && (
							<Text.Body as="p" weight="fwMedium">
								{t('ecomm-coupons:manual-select-search-no-products')}
							</Text.Body>
						)}
					</Flex>
				)}
				{error && (
					<Flex justifyContent="center" my="sp1100">
						<Text.Body as="p" weight="fwMedium">
							{t('ecomm-coupons:manual-select-error')}
						</Text.Body>
					</Flex>
				)}
			</Flex>
			{!error && (
				<Flex mt="sp400" justifyContent="center">
					<Flex.Item>
						<Button.Outline
							type="button"
							disabled={queryParams.offset === 0}
							onClick={(): void => {
								if (queryParams.offset > 0) {
									setQueryParams({
										...queryParams,
										offset: queryParams.offset - 20,
									});
								}
							}}
							mr="sp400"
						>
							{t('ecomm-coupons:manual-select-prev')}
						</Button.Outline>
						<Button.Outline
							type="button"
							disabled={queryParams.offset >= 20 && products?.length === 0}
							onClick={(): void => {
								setQueryParams({
									...queryParams,
									offset: queryParams.offset + 20,
								});
							}}
						>
							{t('ecomm-coupons:manual-select-next')}
						</Button.Outline>
					</Flex.Item>
				</Flex>
			)}

			{!!productValues.length && (
				<SelectedProducts
					value="buyXGetYBuysItemProducts"
					productValues={productValues}
					isModal
					saveChanges={saveChanges}
				/>
			)}
		</Flex>
	);
};
