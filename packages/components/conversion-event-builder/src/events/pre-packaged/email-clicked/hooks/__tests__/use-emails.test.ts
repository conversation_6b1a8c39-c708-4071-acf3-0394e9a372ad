import { renderHook } from '@testing-library/react-hooks';
import { useQuery } from '@tanstack/react-query';
import { useFormattingTokens } from '@activecampaign/platform-core-queries';
import { fetchEmailData, useEmailData } from '../use-emails';

jest.mock('axios');
jest.mock('@tanstack/react-query');
jest.mock('@activecampaign/platform-core-queries');
jest.mock('moment', () => {
	const originalMoment = jest.requireActual('moment');
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	return (): any => originalMoment('2023-08-02T15:43:08-05:00');
});

const mockUseEmailData = {
	campaignOptions: [
		{ label: 'Any Campaign', value: 'any' },
		{ id: 2, name: 'Campaign 1', label: 'Campaign 1 - 08/02/2023', value: 2 },
	],
	oneToOneOptions: [
		{ label: 'Any 1:1 Email', value: 'any' },
		{ label: 'Direct 1:1 Email', value: 'direct' },
		{ label: 'Automated 1:1 Email', value: 'automated' },
		{ id: 1, automation: 'Automation 1', label: 'Automation 1', value: 1 },
	],
	error: null,
	isLoading: false,
};

jest.mock('../use-emails', () => ({
	__esModule: true,
	useEmailData: jest.fn(() => mockUseEmailData),
	fetchEmailData: jest.fn(() => mockUseEmailData),
}));

describe('fetchEmailData', () => {
	it('should fetch email data successfully', async () => {
		const data = fetchEmailData();

		expect(data).toEqual({
			campaignOptions: [
				{ label: 'Any Campaign', value: 'any' },
				{
					id: 2,
					label: 'Campaign 1 - 08/02/2023',
					name: 'Campaign 1',
					value: 2,
				},
			],
			error: null,
			isLoading: false,
			oneToOneOptions: [
				{ label: 'Any 1:1 Email', value: 'any' },
				{ label: 'Direct 1:1 Email', value: 'direct' },
				{ label: 'Automated 1:1 Email', value: 'automated' },
				{ automation: 'Automation 1', id: 1, label: 'Automation 1', value: 1 },
			],
		});
	});
});

describe('useEmailData', () => {
	const mockUseQuery = useQuery as jest.Mock;
	const mockUseFormattingTokens = useFormattingTokens as jest.Mock;

	beforeEach(() => {
		mockUseFormattingTokens.mockReturnValue({ dateFormat: 'MM/DD/YYYY' });
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should return email data correctly', async () => {
		const { result } = renderHook(() =>
			useEmailData({ hasDeals: true, hasOneToOneEmail: true })
		);

		expect(result.current.campaignOptions).toEqual([
			{ label: 'Any Campaign', value: 'any' },
			{ id: 2, name: 'Campaign 1', label: 'Campaign 1 - 08/02/2023', value: 2 },
		]);
		expect(result.current.oneToOneOptions).toEqual([
			{ label: 'Any 1:1 Email', value: 'any' },
			{ label: 'Direct 1:1 Email', value: 'direct' },
			{ label: 'Automated 1:1 Email', value: 'automated' },
			{ id: 1, automation: 'Automation 1', label: 'Automation 1', value: 1 },
		]);
		expect(result.current.error).toBeNull();
		expect(result.current.isLoading).toBe(false);
	});

	it('should handle errors in useEmailData', async () => {
		const errorMessage = 'Error fetching data';
		mockUseEmailData.error = new Error(errorMessage);
		mockUseQuery.mockReturnValue({
			data: [],
			error: new Error(errorMessage),
			isLoading: false,
		});

		const { result } = renderHook(() =>
			useEmailData({ hasDeals: true, hasOneToOneEmail: true })
		);

		expect(result.current.error).toEqual(new Error(errorMessage));
		expect(result.current.isLoading).toBe(false);
	});

	it('should return loading state correctly', async () => {
		mockUseQuery.mockReturnValue({ data: [], error: null, isLoading: true });
		mockUseEmailData.isLoading = true;

		const { result } = renderHook(() =>
			useEmailData({ hasDeals: true, hasOneToOneEmail: true })
		);

		expect(result.current.isLoading).toBe(true);
	});
});
