import axios from 'axios';
import { fetchLinkData } from '../use-link-data';

jest.mock('axios');
jest.mock('@tanstack/react-query');

describe('fetchLinkData', () => {
	it('should fetch link data for campaigns successfully', async () => {
		const mockData = { links: [{ id: 1, link: 'http://example.com' }] };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValueOnce({ data: mockData });

		const data = await fetchLinkData({ type: 'campaign', id: 123 });

		expect(data).toEqual(mockData);
		expect(axios.get).toHaveBeenCalledWith(
			'/api/3/links?filters[campaignid]=123&limit=0&filters[tracked]&filters[link]=click'
		);
	});

	it('should fetch link data for one-to-one emails successfully', async () => {
		const mockData = { links: [{ id: 2, link: 'http://example2.com' }] };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValueOnce({ data: mockData });

		const data = await fetchLinkData({ type: 'oneToOneEmail', id: 456 });

		expect(data).toEqual(mockData);
		expect(axios.get).toHaveBeenCalledWith(
			'/api/3/links?filters[messageid]=456&limit=0&filters[tracked]&filters[link]=click'
		);
	});

	it('should handle errors while fetching link data', async () => {
		const errorMessage = 'Network Error';
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockRejectedValueOnce(new Error(errorMessage));

		await expect(fetchLinkData({ type: 'campaign', id: 123 })).rejects.toThrow(
			errorMessage
		);
		expect(axios.get).toHaveBeenCalledWith(
			'/api/3/links?filters[campaignid]=123&limit=0&filters[tracked]&filters[link]=click'
		);
	});
});
