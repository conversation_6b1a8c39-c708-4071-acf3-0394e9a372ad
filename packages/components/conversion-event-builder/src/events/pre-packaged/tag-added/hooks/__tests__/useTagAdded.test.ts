import axios from 'axios';
import { renderHook } from '@testing-library/react-hooks';
import { useQuery } from '@tanstack/react-query';
import { fetchTagAddedList, useTagAdded } from '../useTagAdded';

jest.mock('axios');
jest.mock('@tanstack/react-query', () => ({
	useQuery: jest.fn(),
}));

describe('fetchTagAddedList', () => {
	it('should fetch tag added list successfully', async () => {
		const mockData = { tags: [{ id: '1', tag: 'Tag 1' }] };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValue({ data: mockData });

		const result = await fetchTagAddedList('test');

		expect(axios.get).toHaveBeenCalledWith(
			'/api/3/tags?type=contact&search=test'
		);
		expect(result).toEqual(mockData);
	});

	it('should handle empty search input', async () => {
		const mockData = { tags: [{ id: '1', tag: 'Tag 1' }] };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValue({ data: mockData });

		const result = await fetchTagAddedList('');

		expect(axios.get).toHaveBeenCalledWith('/api/3/tags?type=contact&search=');
		expect(result).toEqual(mockData);
	});

	it('should throw an error when the request fails', async () => {
		const mockError = new Error('Network Error');
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockRejectedValue(mockError);

		await expect(fetchTagAddedList('test')).rejects.toThrow('Network Error');
		expect(axios.get).toHaveBeenCalledWith(
			'/api/3/tags?type=contact&search=test'
		);
	});
});

describe('useTagAdded', () => {
	it('should return loading state initially', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: null,
			isLoading: true,
			isError: false,
		});

		const { result } = renderHook(() => useTagAdded(''));

		expect(result.current.isLoading).toBe(true);
		expect(result.current.isError).toBe(false);
		expect(result.current.options).toEqual([]);
	});

	it('should return error state', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: null,
			isLoading: false,
			isError: true,
		});

		const { result } = renderHook(() => useTagAdded(''));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(true);
		expect(result.current.options).toEqual([]);
	});

	it('should return data when fetched successfully', () => {
		const mockData = {
			tags: [
				{ id: '1', tag: 'Tag 1' },
				{ id: '2', tag: 'Tag 2' },
			],
		};

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: mockData,
			isLoading: false,
			isError: false,
		});

		const { result } = renderHook(() => useTagAdded(''));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(false);
		expect(result.current.options).toEqual([
			{ id: '1', tag: 'Tag 1', label: 'Tag 1', value: '1' },
			{ id: '2', tag: 'Tag 2', label: 'Tag 2', value: '2' },
		]);
		expect(result.current.hasTags).toBe(true);
	});

	it('should handle empty data', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: { tags: [] },
			isLoading: false,
			isError: false,
		});

		const { result } = renderHook(() => useTagAdded(''));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(false);
		expect(result.current.options).toEqual([]);
		expect(result.current.hasTags).toBe(false);
	});
});
