import axios from 'axios';
import { renderHook } from '@testing-library/react-hooks';
import { useQuery } from '@tanstack/react-query';
import { fetchFormsList, useFormSubmitted } from '../useFormSubmitted';

jest.mock('axios');
jest.mock('@tanstack/react-query', () => ({
	useQuery: jest.fn(),
}));

describe('fetchFormsList', () => {
	it('should fetch forms list successfully', async () => {
		const mockData = { forms: [{ id: '1', name: 'Form 1' }] };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValue({ data: mockData });

		const result = await fetchFormsList('test');

		expect(axios.get).toHaveBeenCalledWith('/api/3/forms&search=test');
		expect(result).toEqual(mockData);
	});

	it('should handle empty search input', async () => {
		const mockData = { forms: [{ id: '1', name: 'Form 1' }] };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValue({ data: mockData });

		const result = await fetchFormsList('');

		expect(axios.get).toHaveBeenCalledWith('/api/3/forms&search=');
		expect(result).toEqual(mockData);
	});

	it('should throw an error when the request fails', async () => {
		const mockError = new Error('Network Error');
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockRejectedValue(mockError);

		await expect(fetchFormsList('test')).rejects.toThrow('Network Error');
		expect(axios.get).toHaveBeenCalledWith('/api/3/forms&search=test');
	});
});

describe('useFormSubmitted', () => {
	it('should return loading state initially', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: null,
			isLoading: true,
			isError: false,
		});

		const { result } = renderHook(() => useFormSubmitted(''));

		expect(result.current.isLoading).toBe(true);
		expect(result.current.isError).toBe(false);
		expect(result.current.options).toEqual([]);
	});

	it('should return error state', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: null,
			isLoading: false,
			isError: true,
		});

		const { result } = renderHook(() => useFormSubmitted(''));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(true);
		expect(result.current.options).toEqual([]);
	});

	it('should return data when fetched successfully', () => {
		const mockData = {
			forms: [
				{ id: '1', name: 'Form 1' },
				{ id: '2', name: 'Form 2' },
			],
		};

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: mockData,
			isLoading: false,
			isError: false,
		});

		const { result } = renderHook(() => useFormSubmitted(''));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(false);
		expect(result.current.options).toEqual([
			{ id: '1', name: 'Form 1', label: 'Form 1', value: '1' },
			{ id: '2', name: 'Form 2', label: 'Form 2', value: '2' },
		]);
		expect(result.current.hasForms).toBe(true);
	});

	it('should handle empty data', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: { forms: [] },
			isLoading: false,
			isError: false,
		});

		const { result } = renderHook(() => useFormSubmitted(''));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(false);
		expect(result.current.options).toEqual([]);
		expect(result.current.hasForms).toBe(false);
	});
});
