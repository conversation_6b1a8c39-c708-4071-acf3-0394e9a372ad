import { renderHook } from '@testing-library/react-hooks';
import { useTranslation } from '@activecampaign/core-translations-client';
import { useQuery } from '@tanstack/react-query';
import { useContactList, fetchContactList } from '../useContactList';
import axios from 'axios';

jest.mock('@activecampaign/core-translations-client', () => ({
	useTranslation: jest.fn(),
}));

jest.mock('@tanstack/react-query', () => ({
	useQuery: jest.fn(),
}));

jest.mock('axios');

describe('useContactList()', () => {
	const mockTranslation = {
		t: (key): string => key,
	};

	beforeEach(() => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useTranslation as any).mockReturnValue(mockTranslation);
	});

	it('should return loading state initially', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: null,
			isLoading: true,
			isError: false,
		});

		const { result } = renderHook(() => useContactList(''));

		expect(result.current.isLoading).toBe(true);
		expect(result.current.isError).toBe(false);
		expect(result.current.listOptions).toBeUndefined();
	});

	it('should return error state', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: null,
			isLoading: false,
			isError: true,
		});

		const { result } = renderHook(() => useContactList(''));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(true);
		expect(result.current.listOptions).toBeUndefined();
	});

	it('should return data when fetched successfully', () => {
		const mockData = {
			lists: [
				{ id: '1', name: 'List 1' },
				{ id: '2', name: 'List 2' },
			],
		};

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: mockData,
			isLoading: false,
			isError: false,
		});

		const { result } = renderHook(() => useContactList(''));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(false);
		expect(result.current.listOptions).toEqual([
			{ id: '1', name: 'List 1', label: 'List 1', value: '1' },
			{ id: '2', name: 'List 2', label: 'List 2', value: '2' },
		]);
	});

	it('should handle empty data', () => {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(useQuery as any).mockReturnValue({
			data: { lists: [] },
			isLoading: false,
			isError: false,
		});

		const { result } = renderHook(() => useContactList(''));

		expect(result.current.isLoading).toBe(false);
		expect(result.current.isError).toBe(false);
		expect(result.current.listOptions).toEqual([]);
		expect(result.current.hasLists).toBe(false);
	});
});

describe('fetchContactList', () => {
	it('should fetch contact list successfully', async () => {
		const mockData = { lists: [{ id: '1', name: 'List 1' }] };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValue({ data: mockData });

		const result = await fetchContactList('test');

		expect(axios.get).toHaveBeenCalledWith('/api/3/lists?filters[name]=test');
		expect(result).toEqual(mockData);
	});

	it('should handle empty search input', async () => {
		const mockData = { lists: [{ id: '1', name: 'List 1' }] };
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockResolvedValue({ data: mockData });

		const result = await fetchContactList('');

		expect(axios.get).toHaveBeenCalledWith('/api/3/lists?filters[name]=');
		expect(result).toEqual(mockData);
	});

	it('should throw an error when the request fails', async () => {
		const mockError = new Error('Network Error');
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(axios.get as any).mockRejectedValue(mockError);

		await expect(fetchContactList('test')).rejects.toThrow('Network Error');
		expect(axios.get).toHaveBeenCalledWith('/api/3/lists?filters[name]=test');
	});
});
