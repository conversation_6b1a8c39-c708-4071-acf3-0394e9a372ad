import '@testing-library/jest-dom';
import { renderHook } from '@testing-library/react-hooks';
import { useQuery } from '@tanstack/react-query';

import { mockMappings } from '../../mocks/mapping';
import { useTranslationsMapGet } from '../use-translations-map-get';
import { useTranslationsMapPost } from '../use-translations-map-post';

jest.mock('@tanstack/react-query');
jest.mock('../use-translations-map-post');

describe('useTranslationsMapGet', () => {
	beforeEach(() => {
		(useQuery as jest.Mock).mockImplementation(() => ({
			data: mockMappings,
			isLoading: false,
		}));

		(useTranslationsMapPost as jest.Mock).mockImplementation(() => ({
			mutate: jest.fn(),
			isLoading: true,
		}));
	});

	it('should return data when the API call is successful', async () => {
		const { result } = renderHook(() => useTranslationsMapGet());

		expect(result.current.data).toBeTruthy();
		expect(result.current.isLoading).toBeFalsy();
	});

	it('should throw an error when the API call fails', async () => {
		(useQuery as jest.Mock).mockImplementation(() => ({
			data: null,
			isLoading: false,
		}));

		const { result } = renderHook(() => useTranslationsMapGet());

		expect(result.current.data).toBeFalsy();
		expect(result.current.isLoading).toBeFalsy();
	});
});
