import { useQuery, UseQueryResult } from '@tanstack/react-query';
import {
	AccountCustomFieldMetum,
	Audience,
	Automation,
	Campaign,
	Connection,
	Conversion,
	CustomField,
	CustomFieldOption,
	CustomObjectSchema,
	DealCustomFieldMetum,
	DealGroup,
	DealRole,
	DealStage,
	EcomOrderDiscount,
	EventTrackingEvent,
	FbAudience,
	Form,
	GlobalData,
	Goal,
	Link,
	List,
	ListEntitlements,
	OutboundEmail,
	Score,
	SiteMessage,
	Sms,
	Tag,
	TaskOutcome,
	DealTaskType,
	Timezone,
	User,
	WhatsAppFlow,
} from '../types';
import { useEntitlements } from './queries/entitlements';
import * as api from '@src/api';
import axios from 'axios';
import { useFeatureFlag } from './useFeatureFlag';

type SegmentBuilderData = {
	audiences: Audience[];
	automations: Automation[];
	campaigns: Campaign[];
	connections: Connection[];
	conversions: Conversion[];
	customFields: CustomField[];
	customFieldOptions: CustomFieldOption[];
	customAccountFields: AccountCustomFieldMetum[];
	customDealFields: DealCustomFieldMetum[];
	customObjects: CustomObjectSchema[];
	dealGroups: DealGroup[];
	dealStages: DealStage[];
	dealRoles: DealRole[];
	ecomOrderDiscounts: EcomOrderDiscount[];
	eventTrackingEvents: EventTrackingEvent[];
	fbAudiences: FbAudience[];
	forms: Form[];
	globalData: GlobalData;
	goals: Goal[];
	links: Link[];
	lists: List[];
	outboundEmails: OutboundEmail[];
	scores: Score[];
	siteMessages: SiteMessage[];
	sms: Sms[];
	smsBroadcast: Sms[];
	tags: Tag[];
	taskOutcomes: TaskOutcome[];
	dealTaskTypes: DealTaskType[];
	timezones: Timezone[];
	users: User[];
	whatsAppFlows?: WhatsAppFlow[];
	whatsAppTemplates?: WhatsAppFlow[];
};

type SegmentDataQuery = {
	data: SegmentBuilderData | undefined;
	isFetched: boolean;
	isFetching: boolean;
	isLoading: boolean;
	isError: boolean;
	errors: unknown[];
};

export const getQueryData = <R>(
	{ isError, data }: UseQueryResult,
	dataKey: string
): R[] => {
	if (isError || data === undefined) {
		return [];
	}
	return data[dataKey] || [];
};

export const hasEntitlement = (
	fetchedEntitlements: boolean,
	isErrorEntitlements: boolean,
	accountEntitlements: ListEntitlements,
	allowedEntitlements: string[]
): boolean => {
	if (allowedEntitlements.length == 0) {
		// No entitlement limitations
		return true;
	}
	if (!fetchedEntitlements) {
		return false;
	}
	if (isErrorEntitlements) {
		// Fallback behavior is to pre-entitlements, so call everything
		return true;
	}
	const entitlements = accountEntitlements.map((obj) => {
		return obj.code;
	});
	return allowedEntitlements.some((entitlement) =>
		entitlements.includes(entitlement)
	);
};

export const useSegmentData = (isCoreData = false): SegmentDataQuery => {
	const {
		data: entitlementsData,
		isFetched: fetchedEntitlements,
		isFetching: isFetchingEntitlements,
		isError: isErrorEntitlements,
	} = useEntitlements();

	const isOmnichannelFeatureFlagEnabeled = useFeatureFlag(
		'omnichannel-form-changes'
	);

	const isWhatsAppEnabled = useFeatureFlag('automations-whatsapp');

	const isEnableQuery = isCoreData ? false : fetchedEntitlements;

	const automationsQuery = useQuery(
		[api.Automations],
		() => api.Automations.list(),
		{ enabled: isEnableQuery }
	);

	const campaignsQuery = useQuery(
		['/api/3/campaigns?orders[sdate]=desc'],
		() => api.CampaignsOrderedByNewestSDate.list(),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['campaigns']
			),
		}
	);
	const connectionsQuery = useQuery(
		[api.Connections],
		() => api.Connections.list(),
		{
			enabled:
				hasEntitlement(isEnableQuery, isErrorEntitlements, entitlementsData, [
					'ecommerce',
				]) ||
				hasEntitlement(isEnableQuery, isErrorEntitlements, entitlementsData, [
					'ecom-core',
				]),
		}
	);
	const conversionsQuery = useQuery(
		[api.Conversions],
		() => api.Conversions.list(0),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['conversions']
			),
		}
	);
	const customAccountFieldsQuery = useQuery(
		[api.AccountCustomFieldMeta],
		() => api.AccountCustomFieldMeta.list(),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['accounts']
			),
		}
	);
	const customDealFieldsQuery = useQuery(
		[api.DealCustomFieldMeta],
		() => api.DealCustomFieldMeta.list(),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['deals']
			),
		}
	);
	const audiencesQuery = useQuery(
		['/api/3/audiences?sort=name&page_size=100'],
		() =>
			axios
				.get('/api/3/audiences?sort=name&page_size=100')
				.then((response) => response.data)
				//TO DO: Audiences API is not ready yet, it is returning 404
				//Supressing this API error until it gets ready
				.catch(() => [])
	);
	const customFieldsQuery = useQuery(
		['/api/3/fields?exclude_field_rel=true&all=true'],
		() =>
			axios
				.get('/api/3/fields?exclude_field_rel=true&all=true')
				.then((response) => response.data)
				.catch((error) => Promise.reject(error.response.data)),
		{
			enabled: isEnableQuery,
		}
	);
	const customObjectsQuery = useQuery(
		[
			'/api/3/customObjects/schemas?filters[relationships.id][eq]=primary-contact&filters[relationships.namespace][eq]=contacts&limit=100&showMetadata=all',
		],
		() =>
			axios
				.get(
					'/api/3/customObjects/schemas?filters[relationships.id][eq]=primary-contact&filters[relationships.namespace][eq]=contacts&limit=100&showMetadata=all'
				)
				.then((response) => response.data)
				.catch((error) => Promise.reject(error.response.data)),
		{
			enabled: isEnableQuery,
		}
	);
	const dealGroupsQuery = useQuery(
		[api.DealGroups],
		() => api.DealGroups.list(),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['deals']
			),
		}
	);
	const dealRolesQuery = useQuery({
		queryKey: [api.DealRoles],
		queryFn: () => api.DealRoles.list(),
		enabled: hasEntitlement(
			isEnableQuery,
			isErrorEntitlements,
			entitlementsData,
			['deals']
		),
	});
	const ecomOrderDiscountsQuery = useQuery({
		queryKey: [api.EcomOrderDiscounts, 40, 'uniqueName'],
		queryFn: () => api.EcomOrderDiscounts.list(40, null, 'name'),
		enabled:
			hasEntitlement(isEnableQuery, isErrorEntitlements, entitlementsData, [
				'ecommerce',
			]) ||
			hasEntitlement(isEnableQuery, isErrorEntitlements, entitlementsData, [
				'ecom-core',
			]),
	});
	const eventTrackingEventsQuery = useQuery(
		[api.EventTrackingEvents],
		() => api.EventTrackingEvents.list(),
		{
			enabled: isEnableQuery,
		}
	);
	const fbAudiencesQuery = useQuery(
		[api.FbAudiences],
		() => api.FbAudiences.list(),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['facebook-custom-audiences']
			),
		}
	);
	const formsQuery = useQuery({
		queryKey: [api.Forms],
		queryFn: () => api.Forms.list(),
		enabled: isEnableQuery,
	});
	const globalDataQuery = useQuery(
		['global-data'],
		() => api.GlobalData.get(),
		{
			enabled: fetchedEntitlements,
		}
	);
	const goalsQuery = useQuery({
		queryKey: [api.Goals],
		queryFn: () => api.Goals.list(),
		enabled: isEnableQuery,
	});
	const linksQuery = useQuery({
		queryKey: [api.Links],
		queryFn: () => api.Links.list(),
		enabled: hasEntitlement(
			isEnableQuery,
			isErrorEntitlements,
			entitlementsData,
			['campaigns']
		),
	});
	const listsQuery = useQuery(
		[api.Lists],
		() =>
			api.Lists.list(
				null,
				null,
				null,
				isOmnichannelFeatureFlagEnabeled ? { 'filters[channel]': 'all' } : {}
			),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['lists']
			),
		}
	);
	const outboundEmailsQuery = useQuery(
		[api.OutboundEmails],
		() => api.OutboundEmails.list(),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['one-to-one-email']
			),
		}
	);
	const scoresQuery = useQuery([api.Scores], () => api.Scores.list(0), {
		enabled: hasEntitlement(
			isEnableQuery,
			isErrorEntitlements,
			entitlementsData,
			['contact-scoring', 'deals']
		),
	});
	const siteMessagesQuery = useQuery(
		[api.SiteMessages, 40],
		() => api.SiteMessages.list(40),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['site-messages']
			),
		}
	);
	const smsQuery = useQuery(
		['/api/3/sms?removeEmptyNames=1'],
		() =>
			axios
				.get('/api/3/sms?removeEmptyNames=1')
				.then((response) => response.data)
				.catch((error) => Promise.reject(error.response.data)),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['sms']
			),
		}
	);
	const smsBroadcastQuery = useQuery(
		['/api/3/sms/broadcasts'],
		() =>
			axios
				.get('/api/3/sms/broadcasts')
				.then((response) => response.data)
				.catch((error) => Promise.reject(error.response.data)),
		{
			enabled: hasEntitlement(
				isEnableQuery,
				isErrorEntitlements,
				entitlementsData,
				['sms']
			),
		}
	);

	const whatsAppFlowsQuery = useQuery(
		['/api/3/channel/whatsapp/integrations/activecampaign/flow'],
		() =>
			axios
				.get('/api/3/channel/whatsapp/integrations/activecampaign/flow')
				.then((response) => response.data)
				.catch((error) => Promise.reject(error.response.data)),
		{
			enabled:
				isWhatsAppEnabled &&
				hasEntitlement(isEnableQuery, isErrorEntitlements, entitlementsData, [
					'whatsapp-inbox',
				]),
		}
	);

	const whatsAppTemplatesQuery = useQuery(
		['/api/3/channel/whatsapp/integrations/activecampaign/whatsapp-template'],
		() =>
			axios
				.get(
					'/api/3/channel/whatsapp/integrations/activecampaign/whatsapp-template'
				)
				.then((response) => response.data)
				.catch((error) => Promise.reject(error.response.data)),
		{
			enabled:
				isWhatsAppEnabled &&
				hasEntitlement(isEnableQuery, isErrorEntitlements, entitlementsData, [
					'whatsapp-inbox',
				]),
		}
	);

	const tagsQuery = useQuery(
		['/api/3/tags?limit=100&order=alpha'],
		() =>
			axios
				.get('/api/3/tags?limit=100&order=alpha')
				.then((response) => response.data)
				.catch((error) => Promise.reject(error.response.data)),
		{
			enabled: isEnableQuery,
		}
	);

	const timezonesQuery = useQuery([api.Timezones], () => api.Timezones.list(), {
		enabled: isEnableQuery,
	});
	const usersQuery = useQuery([api.Users], () => api.Users.list(), {
		enabled: hasEntitlement(
			isEnableQuery,
			isErrorEntitlements,
			entitlementsData,
			['deals', 'accounts']
		),
	});

	const taskOutcomesQuery = useQuery([api.TaskOutcomes], () =>
		api.TaskOutcomes.list()
	);

	const dealTaskTypesQuery = useQuery([api.DealTaskTypes], () =>
		api.DealTaskTypes.list()
	);

	// array of queries to be used to check state
	const queriesCoreData = [globalDataQuery];

	// array of queries to be used to check state
	const queriesAllData = [
		audiencesQuery,
		automationsQuery,
		campaignsQuery,
		connectionsQuery,
		conversionsQuery,
		customAccountFieldsQuery,
		customDealFieldsQuery,
		customFieldsQuery,
		customObjectsQuery,
		dealGroupsQuery,
		dealRolesQuery,
		ecomOrderDiscountsQuery,
		eventTrackingEventsQuery,
		fbAudiencesQuery,
		formsQuery,
		globalDataQuery,
		goalsQuery,
		linksQuery,
		listsQuery,
		outboundEmailsQuery,
		scoresQuery,
		siteMessagesQuery,
		smsQuery,
		tagsQuery,
		timezonesQuery,
		usersQuery,
		taskOutcomesQuery,
		dealTaskTypesQuery,
	];

	const queries = isCoreData ? queriesCoreData : queriesAllData;

	const isFetched = queries.every((query) => {
		// idle status will occur if the account does not have the necessary entitlements
		return query.isFetched || query.status == 'idle';
	});

	return {
		data:
			isFetched && fetchedEntitlements
				? {
						audiences: getQueryData(audiencesQuery, 'data'),
						automations: getQueryData<Automation>(
							automationsQuery,
							'automations'
						),
						campaigns: getQueryData<Campaign>(campaignsQuery, 'campaigns'),
						connections: getQueryData<Connection>(
							connectionsQuery,
							'connections'
						),
						conversions: getQueryData<Conversion>(
							conversionsQuery,
							'conversions'
						),
						customAccountFields: getQueryData<AccountCustomFieldMetum>(
							customAccountFieldsQuery,
							'accountCustomFieldMeta'
						),
						customDealFields: getQueryData<DealCustomFieldMetum>(
							customDealFieldsQuery,
							'dealCustomFieldMeta'
						),
						customFields: getQueryData<CustomField>(
							customFieldsQuery,
							'fields'
						),
						customFieldOptions: getQueryData<CustomFieldOption>(
							customFieldsQuery,
							'fieldOptions'
						),
						customObjects: getQueryData<CustomObjectSchema>(
							customObjectsQuery,
							'schemas'
						),
						dealGroups: getQueryData<DealGroup>(dealGroupsQuery, 'dealGroups'),
						dealStages: getQueryData<DealStage>(dealGroupsQuery, 'dealStages'),
						dealRoles: getQueryData<DealRole>(dealRolesQuery, 'dealRoles'),
						ecomOrderDiscounts: getQueryData<EcomOrderDiscount>(
							ecomOrderDiscountsQuery,
							'ecomOrderDiscounts'
						),
						eventTrackingEvents: getQueryData<EventTrackingEvent>(
							eventTrackingEventsQuery,
							'eventTrackingEvents'
						),
						fbAudiences: getQueryData<FbAudience>(
							fbAudiencesQuery,
							'fbAudiences'
						),
						forms: getQueryData<Form>(formsQuery, 'forms'),
						globalData: globalDataQuery.data,
						goals: getQueryData<Goal>(goalsQuery, 'goals'),
						links: getQueryData<Link>(linksQuery, 'links'),
						lists: getQueryData<List>(listsQuery, 'lists'),
						outboundEmails: getQueryData<OutboundEmail>(
							outboundEmailsQuery,
							'outboundEmails'
						),
						scores: getQueryData<Score>(scoresQuery, 'scores'),
						siteMessages: getQueryData<SiteMessage>(
							siteMessagesQuery,
							'siteMessages'
						),
						sms: getQueryData<Sms>(smsQuery, 'sms'),
						smsBroadcast: getQueryData<Sms>(smsBroadcastQuery, 'broadcasts'),
						tags: getQueryData<Tag>(tagsQuery, 'tags'),
						timezones: getQueryData<Timezone>(timezonesQuery, 'timezones'),
						users: getQueryData<User>(usersQuery, 'users'),
						taskOutcomes: getQueryData<TaskOutcome>(
							taskOutcomesQuery,
							'taskOutcomes'
						),
						dealTaskTypes: getQueryData<DealTaskType>(
							dealTaskTypesQuery,
							'dealTasktypes'
						),
						whatsAppFlows: getQueryData<WhatsAppFlow>(
							whatsAppFlowsQuery,
							'results'
						),
						whatsAppTemplates: getQueryData<WhatsAppFlow>(
							whatsAppTemplatesQuery,
							'results'
						),
				  }
				: undefined,
		errors: queries
			.filter((query) => query.isError)
			.map((query) => query.error),
		isFetched: fetchedEntitlements && isFetched,
		isFetching:
			isFetchingEntitlements || queries.some((query) => query.isFetching),
		isError: isErrorEntitlements || queries.some((query) => query.isError),
		isLoading:
			isFetchingEntitlements || queries.some((query) => query.isFetching),
	};
};
