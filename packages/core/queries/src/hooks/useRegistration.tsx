import { useQuery } from '@tanstack/react-query';
import { getRegistration } from '.././api/registration';
import { RegistrationResponse } from '@src/types/registration.types';

export const useRegistration = (): RegistrationResponse => {
	const { data, isError, isFetched, isLoading, isFetching } = useQuery({
		queryKey: [`10dlcRegistration`],
		queryFn: async () => {
			const data = await getRegistration();
			return data.data;
		},
		refetchOnMount: 'always',
		cacheTime: 0,
	});

	return {
		data,
		isError,
		isFetched,
		isLoading,
		isFetching,
	};
};
