import { useQuery, UseQueryResult } from '@tanstack/react-query';

import { DealGroups } from '@src/api';
import { DealGroupsDefaults } from '@src/constants';
import { DealGroup, DealStage, ListDealGroups, QueryToken } from '@src/types';

type UseDealGroups = Pick<
	UseQueryResult<ListDealGroups>,
	'error' | 'isError' | 'isLoading'
> & {
	groups: DealGroup[];
	stages: DealStage[];
};

type Props = {
	offset?: number;
	limit?: number;
};

export function useDealGroups({
	offset = DealGroupsDefaults.Offset,
	limit = DealGroupsDefaults.Limit,
}: Props): UseDealGroups {
	const {
		data: { dealGroups, dealStages },
		error,
		isError,
		isLoading,
	} = useQuery({
		queryKey: QueryToken.DealGroups,
		queryFn: () => DealGroups.list(limit, offset),
		placeholderData: { dealGroups: [], dealStages: [], meta: { total: 0 } },
	});

	return {
		groups: dealGroups,
		stages: dealStages,
		error,
		isError,
		isLoading,
	};
}
