import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { QueuedSubscribers as QueuedSubscribersApi } from '../api/api';
import { DynamicObj, QueryToken } from '../types';

export function useQueueSubscribers(
	blockId: number
): UseQueryResult<DynamicObj> {
	const queryKey = [`${QueryToken.QueueSubscribers}`, `${blockId}`];

	return useQuery({
		queryKey,
		queryFn: () => QueuedSubscribersApi.get(blockId),
	});
}
