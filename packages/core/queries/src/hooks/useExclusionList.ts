import {
	useQuery,
	useMutation,
	UseQueryResult,
	UseMutationResult,
	useQueryClient,
} from '@tanstack/react-query';

import { ExclusionList, ExclusionListItem } from '@src/api';
import {
	createToast,
	ToastDuration,
} from '@activecampaign/camp-components-toast';
import { useTranslation } from '@activecampaign/core-translations-client';

type UseExclusionList = {
	getSingleResult: UseQueryResult<ExclusionListItem>;
	mutateSingle: UseMutationResult<ExclusionListItem>;
	createExclusion: UseMutationResult<ExclusionListItem>;
};
export function useExclusionList(
	exclusionId: string | null,
	additionalAction?: () => void
): UseExclusionList {
	const { t } = useTranslation();
	const queryClient = useQueryClient();

	const getSingleResult = useQuery({
		queryKey: ['exclusionList', exclusionId],
		queryFn: () => {
			if (exclusionId) {
				return ExclusionList.get(exclusionId);
			}
		},
	});

	const mutateSingle = useMutation(
		async (data: ExclusionListItem) => {
			return ExclusionList.put(exclusionId, data);
		},
		{
			onSuccess: (data) => {
				createToast({
					description: t('contacts:list-exclusions:pattern-update-description'),
					appearance: 'success',
					title: t('contacts:list-exclusions:pattern-update-title'),
					duration: ToastDuration.Short,
				});

				queryClient.setQueryData(['exclusionList', exclusionId], data);

				if (additionalAction) {
					additionalAction();
				}
			},
			onError: () => {
				createToast({
					description: t('contacts:list-exclusions:pattern-update-err-desc'),
					appearance: 'danger',
					title: t('global:something-went-wrong'),
					duration: ToastDuration.Standard,
				});
			},
		}
	);

	const createExclusion = useMutation(
		async (exclusionFormData: Omit<ExclusionListItem, 'id'>) =>
			ExclusionList.post(exclusionFormData),
		{
			onSuccess: () => {
				createToast({
					description: t('contacts:list-exclusions:pattern-create-description'),
					appearance: 'success',
					title: t('contacts:list-exclusions:pattern-create-title'),
					duration: ToastDuration.Short,
				});

				if (additionalAction) {
					additionalAction();
				}
			},
			onError: () => {
				createToast({
					description: t('contacts:list-exclusions:pattern-create-err-desc'),
					appearance: 'danger',
					title: t('global:something-went-wrong'),
					duration: ToastDuration.Standard,
				});
			},
		}
	);

	return {
		getSingleResult,
		mutateSingle,
		createExclusion,
	};
}
