import { useQuery } from '@tanstack/react-query';
import { PhoneOnlyResponse } from '../types';
import { PHONE_ONLY_SETTINGS } from '../constants';
import { getPhoneSettings } from '../api/contactSettings';

export type PhoneOnlySettings = {
	phoneSettings: PhoneOnlyResponse;
	isPhoneSettingsLoading: boolean;
	hasPhoneSettingsError: boolean;
};

export const usePhoneOnlySettings = (): PhoneOnlySettings => {
	const {
		data: phoneSettings,
		isLoading: isPhoneSettingsLoading,
		isError: hasPhoneSettingsError,
	} = useQuery({
		queryKey: PHONE_ONLY_SETTINGS,
		queryFn: getPhoneSettings,
		cacheTime: 0,
	});

	return {
		phoneSettings,
		isPhoneSettingsLoading,
		hasPhoneSettingsError,
	};
};
