import { ApiV3Dao } from 'api';

import {
	useInfiniteQuery,
	UseInfiniteQueryOptions,
	UseInfiniteQueryResult,
	useQuery,
	UseQueryOptions,
	UseQueryResult,
} from '@tanstack/react-query';

export type QueryOptions<R> = Omit<
	UseQueryOptions<R, unknown, R, string[]>,
	'queryKey' | 'queryFn'
>;

type GetQueryHook<G> = (
	id: string | number,
	options?: QueryOptions<G>
) => UseQueryResult<G>;

type ListQueryHook<L> = (
	limit?: number,
	offset?: number,
	options?: QueryOptions<L>,
	unique?: string,
	restParams?: Record<string, string>
) => UseQueryResult<L>;

type ListInfiniteQueryHook<L> = (
	limit?: number,
	options?: UseInfiniteQueryOptions<L>
) => UseInfiniteQueryResult<L>;

export const getQueryFactory =
	<G, L>(dao: ApiV3Dao<G, L>): GetQueryHook<G> =>
	(id: string | number, options?: QueryOptions<G>): UseQueryResult<G> =>
		useQuery({
			queryKey: [dao.endpoint, id],
			queryFn: () => dao.get(id),
			...options,
		});

export const listQueryFactory =
	<G, L>(dao: ApiV3Dao<G, L>): ListQueryHook<L> =>
	(
		limit?: number,
		offset?: number,
		options?: QueryOptions<L>,
		unique?: string,
		restParams?: Record<string, string>
	): UseQueryResult<L> =>
		useQuery({
			queryKey: [dao.endpoint, limit, offset, unique, restParams],
			queryFn: () => dao.list(limit, offset, unique, restParams),
			...options,
		});

// This is the max limit allowed by ResultController in Hosted, and so it is
// the max that many Hosted API endpoints can allow
const LIST_REQUEST_LIMIT = 100;

export const listInfiniteQueryFactory =
	<G, L>(dao: ApiV3Dao<G, L>): ListInfiniteQueryHook<L> =>
	(
		limit = LIST_REQUEST_LIMIT,
		options?: UseInfiniteQueryOptions<L>
	): UseInfiniteQueryResult<L> =>
		useInfiniteQuery(
			[dao.endpoint, 'infinite'],
			({ pageParam }) => dao.list(limit, pageParam),
			{
				// provide a default getNextPageParam function. May need to override if the page response structure is different
				getNextPageParam: (last, pages) => {
					const currentTotal = pages.reduce(
						(a, b) => a + b[dao.endpoint].length,
						0
					);
					const totalAvailable = parseInt(last.meta.total, 10);

					return currentTotal < totalAvailable ? currentTotal : undefined;
				},
				...options,
			}
		);
