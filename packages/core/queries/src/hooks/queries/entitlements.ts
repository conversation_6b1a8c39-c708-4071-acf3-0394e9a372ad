import {
	useQuery,
	UseQueryResult,
	useMutation,
	useQueryClient,
	UseMutationResult,
} from '@tanstack/react-query';
import {
	getQueryCache,
	QUERY_CLIENTS,
	WINDOW_CLIENT_KEY_NEXT,
} from '@activecampaign/platform-core-utilities';
import { Entitlements as EntitlementsApi, EntitlementsRefresh } from '@src/api';
import { ListEntitlements, RefreshEntitlements } from '@src/types/entitlements';

export const useEntitlements = (
	withErrorBoundary = false
): UseQueryResult<ListEntitlements> =>
	useQuery({
		queryKey: ['entitlements'],
		queryFn: async () => {
			try {
				// query both the v3 & v4 caches for global data
				const data = await getQueryCache(
					['entitlements'],
					QUERY_CLIENTS.LEGACY
				);

				if (data) {
					// return the cache data if it exists
					return data;
				}

				// eslint-disable-next-line no-empty
			} catch {}
			// otherwise make the api call to entitlements
			return EntitlementsApi.list();
		},
		useErrorBoundary: withErrorBoundary ? true : false,
	});

export const useListEntitlementsQuery = useEntitlements;

export const useRefreshEntitlements =
	(): UseMutationResult<RefreshEntitlements> => {
		const queryClient = useQueryClient();

		return useMutation({
			mutationFn: () => EntitlementsRefresh.post(),
			onSuccess: () => {
				queryClient.invalidateQueries({
					queryKey: ['entitlements'],
				});
				//invalidate the next cache
				const client = window[WINDOW_CLIENT_KEY_NEXT];
				client?.invalidateQueries?.(['entitlements']);
			},
		});
	};
