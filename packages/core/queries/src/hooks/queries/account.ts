import {
	useMutation,
	UseMutationResult,
	useQueryClient,
} from '@tanstack/react-query';

import { Accounts } from '@src/api';
import {
	createToast,
	ToastDuration,
} from '@activecampaign/camp-components-toast';
import { useTranslation } from '@activecampaign/core-translations-client';
import { CreateAccountResponse } from '@src/types';

type UseAccount = {
	createAccount: UseMutationResult<CreateAccountResponse>;
};
export function useAccount(): UseAccount {
	const { t } = useTranslation();
	const queryClient = useQueryClient();

	const createAccount = useMutation(
		async (accountName: string) =>
			Accounts.post({ account: { name: accountName } }),
		{
			onSuccess: () => {
				createToast({
					appearance: 'success',
					title: t('accounts:creation:success-title'),
					duration: ToastDuration.Short,
				});

				queryClient.invalidateQueries({
					queryKey: ['accounts'],
				});
			},
			onError: () => {
				createToast({
					description: t('accounts:creation:error-description'),
					appearance: 'danger',
					title: t('accounts:creation:error-title'),
					duration: ToastDuration.Standard,
				});
			},
		}
	);

	return {
		createAccount,
	};
}
