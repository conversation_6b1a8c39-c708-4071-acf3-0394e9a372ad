import {
	useMutation,
	UseMutationResult,
	useQueryClient,
} from '@tanstack/react-query';
import { BulkUpdate, BulkUpdateValidation } from '@src/api';
import {
	createToast,
	ToastDuration,
} from '@activecampaign/camp-components-toast';
import { useTranslation } from '@activecampaign/core-translations-client';
import { ContactsBulkUpdate } from '@src/types';

type UseContactsType = {
	bulkUpdate: UseMutationResult;
	validateBulkUpdate: UseMutationResult;
};
export const useContacts = (): UseContactsType => {
	const { t } = useTranslation();
	const queryClient = useQueryClient();

	const bulkUpdate = useMutation(
		async (payload: ContactsBulkUpdate) => BulkUpdate.post(payload),
		{
			onSuccess: () => {
				createToast({
					description: t('contacts:bulkedit:update-description'),
					appearance: 'success',
					title: t('contacts:bulkedit:update-title'),
					duration: ToastDuration.Short,
				});

				queryClient.invalidateQueries({
					queryKey: ['tags'],
				});
			},
			onError: (err: Error) => {
				createToast({
					description: err.message,
					appearance: 'danger',
					title: t('global:try-again'),
					duration: ToastDuration.Standard,
				});
			},
		}
	);

	const validateBulkUpdate = useMutation(async (payload: ContactsBulkUpdate) =>
		BulkUpdateValidation.post(payload)
	);

	return {
		bulkUpdate,
		validateBulkUpdate,
	};
};
