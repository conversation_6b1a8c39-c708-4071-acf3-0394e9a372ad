import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { GetSegment } from '../../types';
import { Segments } from '../../api';
import { QueryOptions } from './factories';
import { QueryToken } from '../../types/queries';

export const useGetSegmentQuery = (
	id: string | number,
	timestamp?: number,
	options?: QueryOptions<GetSegment>
): UseQueryResult<GetSegment> => {
	let queryKey, queryFunction;
	if (timestamp || timestamp === 0) {
		queryKey = [QueryToken.SegmentV2, { id: `${id}`, timestamp }];
		queryFunction = (): Promise<GetSegment> =>
			Segments.getPointInTime(id, timestamp);
	} else {
		queryKey = [QueryToken.SegmentV2, `${id}`];
		queryFunction = (): Promise<GetSegment> => Segments.get(id);
	}

	return useQuery({
		queryKey: ['segmentsV2', queryKey],
		queryFn: queryFunction,
		...options,
	});
};

export const usePostSegmentQuery = (
	payload: string,
	enabled: boolean
): UseQueryResult<GetSegment> => {
	return useQuery({
		queryKey: ['segmentsV2/transform-to-v2', payload],
		queryFn: () => Segments.post(payload),
		enabled: enabled,
	});
};
