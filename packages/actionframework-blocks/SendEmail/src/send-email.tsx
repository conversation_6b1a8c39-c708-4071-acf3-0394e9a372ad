import React, { useRef, useState, useEffect } from 'react';
import { Formik, Form, FormikErrors, FormikValues } from 'formik';
import { ValidationError } from 'yup';
import {
	className,
	DEFAULT_SPLIT_MESSAGE_VALUE,
	PRIMARY_SPLIT_MESSAGE_VALUE,
	DEFAULT_TABS_LIST,
	finishValidationSchema,
	finishSplitValidationSchema,
} from './send-email.constants';
import {
	SendEmailFormValues,
	AutomationSendEmailBlockData,
	StepId,
	CAMPAIGN_STATUS,
} from './send-email.types';
import Styled from '@activecampaign/camp-components-styled';
import LoadingIndicator from '@activecampaign/camp-components-loading-indicator';
import {
	useUpdateCampaignMessage,
	useInitializeFormValues,
	UpdateCampaignParams,
	UpdateMessageParams,
	useCampaignUnschedule,
	useTransformCampaignToSplit,
	useSplitSeriesSave,
} from './hooks';
import Flex from '@activecampaign/camp-components-flex';
import {
	useCampaignFinish,
	getCampaignCacheKey,
	getMessageByCampaignIdCacheKey,
} from './hooks';
import { useQueryClient } from '@tanstack/react-query';
import { SplitTestContextProvider } from './contexts';
import { buildSplitBodyForSave, updateTabList } from './utils';
import Modal from '@activecampaign/camp-components-modal';
import { VariationModalBody } from './components/CanvasBlockHelpers';
import { DynamicObj } from '@activecampaign/platform-core-queries';

export type SendEmailProps = {
	block?: AutomationSendEmailBlockData;
	children?: JSX.Element;
	handleSubmit?: Function;
};

const SendEmail: React.FC<SendEmailProps> = ({
	block,
	children,
	handleSubmit,
}: SendEmailProps) => {
	const { mutate: updateCampaignMessageMutation } = useUpdateCampaignMessage();
	const {
		isLoading,
		isError,
		formValues: initialFormValues,
		isSplit: initialIsSplit,
		tabsList: initialTabsList,
		isFetched: initialFormFetched,
	} = useInitializeFormValues(block);
	const [formIsInitialized, setFormIsInitialized] = useState(false);

	const queryClient = useQueryClient();

	const { mutate: campaignFinishMutation } = useCampaignFinish();
	const { mutateAsync: campaignUnscheduleMutation } = useCampaignUnschedule();
	const { mutate: transformSplitCampaignMutation } =
		useTransformCampaignToSplit();
	const { mutate: splitSeriesSaveMutation } = useSplitSeriesSave();
	const [currentWinnerSelection, setCurrentWinnerSelection] =
		useState<DynamicObj | null>();

	const [isFinalStatus, setIsFinalStatus] = useState(false);

	const [selectedTabValue, setSelectedTabValue] = useState<string>(
		initialIsSplit ? PRIMARY_SPLIT_MESSAGE_VALUE : DEFAULT_SPLIT_MESSAGE_VALUE
	);
	const [tabsList, setTabsList] = useState(
		initialIsSplit ? initialTabsList : DEFAULT_TABS_LIST
	);

	const [deletedTabs, setDeletedTabs] = useState<number[]>([]);

	const [splitTestEnabled, setSplitTestEnabled] =
		useState<boolean>(initialIsSplit);

	//bug fix for coming back from the campaign editor, initialIsSplit populates after first render so need to check if the initial form is fetched to load saved values in split test context
	useEffect(() => {
		if (initialFormFetched && !formIsInitialized && initialIsSplit) {
			setSelectedTabValue(PRIMARY_SPLIT_MESSAGE_VALUE);
			setTabsList(initialTabsList);
			setSplitTestEnabled(initialIsSplit);
			setFormIsInitialized(true);
		}
	}, [
		initialFormFetched,
		formIsInitialized,
		initialIsSplit,
		initialTabsList,
		initialFormValues,
	]);

	const invalidRequestAttributes = useRef<string[]>();

	const setInvalidRequestAttributes = (finalizeErrors): void => {
		/**
		 *  If the campaign is not in draft status, we need to filter out the fields that are incorrect so they don't get updated.
		 *  This is because the API will throw a 409 if we try to update fields that are incorrect.
		 */
		const fieldsThatNeedToBeMapped = {
			// these fields need to be mapped to the correct API field name
			fromEmailDomainVerified: 'fromEmail',
			replyToEmailDomainVerified: 'replyToEmail',
			emailname: 'name',
		};
		invalidRequestAttributes.current = [] as string[];
		finalizeErrors.inner.forEach((error: ValidationError) => {
			if (fieldsThatNeedToBeMapped[error.path]) {
				invalidRequestAttributes.current.push(
					fieldsThatNeedToBeMapped[error.path]
				);
			} else {
				invalidRequestAttributes.current.push(error.path);
			}
		});
	};

	const validateSplit = async (
		values: FormikValues
	): Promise<FormikErrors<FormikValues>> => {
		if (values?.skipValidation) {
			return {};
		}

		let finalizeErrors = {} as FormikErrors<FormikValues>;
		let splitPercent = 0;

		for (const tab of tabsList) {
			splitPercent += tab.percent;
			if (tab.value === selectedTabValue) {
				try {
					await finishSplitValidationSchema.validate(values, {
						abortEarly: false,
					});
				} catch (tabErrors) {
					setInvalidRequestAttributes(tabErrors);
					finalizeErrors = { ...finalizeErrors, ...tabErrors };
				}
			} else {
				try {
					await finishSplitValidationSchema.validate(tab.values, {
						abortEarly: false,
					});
				} catch (tabErrors) {
					setInvalidRequestAttributes(tabErrors);
					finalizeErrors = { ...finalizeErrors, ...tabErrors };
				}
			}
		}
		if (splitPercent !== 100) {
			finalizeErrors = {
				...finalizeErrors,
				...{ errors: ['automations:actions:send:error:no:splitpercent'] },
			};
		} else {
			/* @ts-ignore */
			if (finalizeErrors.errors && finalizeErrors.errors.length > 0) {
				/* @ts-ignore */
				finalizeErrors.errors = finalizeErrors.errors.filter(
					(error) => error === 'automations:actions:send:error:no:splitpercent'
				);
			}
		}
		const hasErrors =
			/* @ts-ignore */
			(finalizeErrors.errors && finalizeErrors.errors.length > 0) ||
			/* @ts-ignore */
			(finalizeErrors.inner && finalizeErrors.inner.length > 0);

		if (hasErrors) {
			setIsFinalStatus(false);
			return finalizeErrors;
		} else {
			setIsFinalStatus(true);
			invalidRequestAttributes.current = [] as string[];
			return {};
		}
	};

	const validate = async (
		values: FormikValues
	): Promise<FormikErrors<FormikValues>> => {
		if (values?.skipValidation) {
			return {};
		}
		try {
			await finishValidationSchema.validate(values, { abortEarly: false });
			setIsFinalStatus(true);
			invalidRequestAttributes.current = [] as string[];
			return {};
		} catch (finalizeErrors) {
			setIsFinalStatus(false);
			if (values.stepid === StepId.EditCampaign) {
				setInvalidRequestAttributes(finalizeErrors);
				return finalizeErrors;
			}
		}
	};

	const buildBlockData = (
		values: SendEmailFormValues,
		block: AutomationSendEmailBlockData
	): AutomationSendEmailBlockData => {
		const { campaignid, sendtype, emailname, hideRecommendationsBlock } =
			values;

		// only save the block data we care about
		return {
			...block,
			params: {
				campaignid,
				sendtype,
				emailname,
				hideRecommendationsBlock,
			},
		};
	};

	const updateCampaignStatus = async (
		campaignStatus,
		campaignId,
		blockData
	): Promise<void> => {
		/**
		 * Campaigns backend doesn't allow for us to directly update the status to 1 (scheduled) in series campaigns and the
		 * finish endpoint being proxied through their Java service doesn't change the 4 status to 1, so we have to
		 * manually move the campaign to draft and then finish it from there. This is read after write, but its the only
		 * way for us to get around making backend changes. If for some reason this fails, nothing will happen and the campaign
		 * will remain in status 4.
		 */
		if (campaignStatus === CAMPAIGN_STATUS.DEACTIVATED) {
			await campaignUnscheduleMutation(campaignId, {});
		}
		campaignFinishMutation(campaignId, {
			onSuccess: async (): Promise<void> => {
				await queryClient.invalidateQueries({
					queryKey: getCampaignCacheKey(campaignId),
				});
				await queryClient.invalidateQueries({
					queryKey: getMessageByCampaignIdCacheKey(campaignId),
				});
				handleSubmit(blockData);
			},
			onError: (error) => {
				// TODO: should we display an error toast if the campaign can't be finished?
				console.error('campaignFinishMutation error:', error);
				// if the campaign status change fails, we still want to save the block data
				handleSubmit(blockData);
			},
		});
	};

	const handleSplitOnSubmit = async ({
		campaignId,
		isSplitDraft,
		values,
		blockData,
		updateCampaignParams,
		updateMessageParams,
	}): Promise<void> => {
		const updatedTabList = updateTabList(tabsList, selectedTabValue, values);
		/* istanbul ignore next: saying line below not covered but there is a success and error tests */
		if (initialIsSplit) {
			// Block is already a split - just need to update the series save
			const splitBody = buildSplitBodyForSave(
				null,
				updatedTabList,
				true,
				deletedTabs
			);
			splitSeriesSaveMutation(
				{ campaignId: campaignId, params: splitBody },
				{
					onSuccess: async (): Promise<void> => {
						await queryClient.invalidateQueries({
							queryKey: getCampaignCacheKey(campaignId),
						});
						await queryClient.invalidateQueries({
							queryKey: getMessageByCampaignIdCacheKey(campaignId),
						});

						if (!isSplitDraft) {
							await updateCampaignStatus(
								values.campaignStatus,
								campaignId,
								blockData
							);
						} else {
							handleSubmit(blockData);
						}
					},
					onError: (error) => {
						console.error('splitSeriesSaveMutation error:', error);
						// if the campaign message fails, we still want to save the block data
						handleSubmit(blockData);
					},
				}
			);
		} else {
			//block needs to be transition to a split campaign
			/* istanbul ignore next: need to figure out a way to update state in order to trigger this */
			transformSplitCampaignMutation(
				{
					campaignId: updateCampaignParams.campaignId,
					messageId: updateMessageParams.messageId,
				},
				{
					onSuccess: async (response): Promise<void> => {
						const { messageId: splitMessageId, campaignId: splitCampaignId } =
							response.data;
						const splitBody = buildSplitBodyForSave(
							splitMessageId,
							updatedTabList,
							false
						);
						const updatedParams = {
							...blockData.params,
							...{ campaignid: splitCampaignId },
						};
						const updatedBlockData = {
							...blockData,
							...{ params: updatedParams },
						};

						splitSeriesSaveMutation(
							{ campaignId: splitCampaignId, params: splitBody },
							{
								onSuccess: async (): Promise<void> => {
									await queryClient.invalidateQueries({
										queryKey: getCampaignCacheKey(splitCampaignId),
									});
									await queryClient.invalidateQueries({
										queryKey: getMessageByCampaignIdCacheKey(splitCampaignId),
									});

									if (!isSplitDraft) {
										await updateCampaignStatus(
											values.campaignStatus,
											splitCampaignId,
											updatedBlockData
										);
									} else {
										handleSubmit(updatedBlockData);
									}
								},
								onError: (error) => {
									console.error('splitSeriesSaveMutation error:', error);
									// if the campaign message fails, we still want to save the block data
									handleSubmit(updatedBlockData);
								},
							}
						);
					},
					onError: (error) => {
						console.error('transformSplitCampaignMutation error:', error);
						// if the campaign message fails, we still want to save the block data
						handleSubmit(blockData);
					},
				}
			);
		}
	};

	const handleEditOnSubmit = (
		values: SendEmailFormValues,
		blockData: AutomationSendEmailBlockData,
		/* istanbul ignore next: cannot trigger save modal draft button via send email tests */
		isSplitDraft = false
	): void => {
		const messageValues = splitTestEnabled ? tabsList[0].values : values;

		const {
			campaignid: campaignId,
			messageId,
			emailname,
			subject,
			preHeader,
			replyTrackingEnabled,
			googleAnalyticsLinkTrackingEnabled,
			readTrackingEnabled,
			linkTrackingEnabled,
			fromName,
			fromEmail,
			replyToEmail,
			skipValidation = false,
		} = messageValues;
		const updateCampaignParams: UpdateCampaignParams = {
			campaignId,
			name: emailname,
			replyTrackingEnabled,
			googleAnalyticsLinkTrackingEnabled,
			readTrackingEnabled,
			linkTrackingEnabled,
		};

		const updateMessageParams: Partial<UpdateMessageParams> = {
			messageId,
			subject,
			preHeader,
			fromName,
			fromEmail,
			replyToEmail,
		};
		/* istanbul ignore next: cannot trigger save as a draft with errors because formik disables submit on the form if there are errors */
		invalidRequestAttributes.current?.forEach((field) => {
			delete updateCampaignParams[field];
			delete updateMessageParams[field];
		});
		updateCampaignMessageMutation(
			{ campaign: updateCampaignParams, message: updateMessageParams },
			{
				onSuccess: async (): Promise<void> => {
					if (splitTestEnabled) {
						await handleSplitOnSubmit({
							campaignId,
							isSplitDraft,
							values,
							blockData,
							updateCampaignParams,
							updateMessageParams,
						});
					} else {
						if (!skipValidation) {
							await updateCampaignStatus(
								values.campaignStatus,
								campaignId,
								blockData
							);
						} else {
							handleSubmit(blockData);
						}
					}
				},
				onError: (error) => {
					// TODO: should we display an error toast if the campaign can't be updated?
					console.error('updateCampaignMessageMutation error:', error);

					// if the campaign message fails, we still want to save the block data
					handleSubmit(blockData);
				},
			}
		);
	};

	/* istanbul ignore next: already being covered by submit tests */
	const handleSubmitWinner = async (
		block: AutomationSendEmailBlockData
	): Promise<void> => {
		await handleSubmit(block);
	};

	const handleOnSubmit = (values: SendEmailFormValues): void => {
		let blockData = buildBlockData(values, block);
		const { stepid, campaignid, submissionType } = values;

		if (stepid === StepId.EditCampaign) {
			if (splitTestEnabled && values?.skipValidation) {
				//This preview panel link button. it saves always to draft status
				handleEditOnSubmit(values, blockData, true);
			} else {
				//This handles Finish Split
				handleEditOnSubmit(values, blockData, false);
			}
		} else if (campaignid === 0) {
			blockData = {
				...blockData,
				params: {
					...blockData.params,
					editingcreateblock: true, // if this is not set hosted will throw a 422
				} as SendEmailFormValues,
			};
			handleSubmit(blockData);
		} else {
			// just update normal block data/params
			handleSubmit(blockData, undefined, submissionType === 'partial');
		}
	};

	if (isLoading) {
		return (
			<Flex
				alignItems="center"
				data-testid="send-email-loading"
				direction="column"
				justifyContent="center"
				m={'sp500'}
			>
				<LoadingIndicator appearance="default" size="medium" />
			</Flex>
		);
	}

	if (isError) {
		// TODO should we display the error toast if the campaign/message can't be loaded?
		return <div data-testid="send-email-error">Error loading form values</div>;
	}

	return (
		<Styled className={className}>
			{currentWinnerSelection && (
				<Modal width="600px" hideDismiss>
					<VariationModalBody
						block={block}
						currentSelectedStackCard={{
							testNumber: currentWinnerSelection?.testNumber,
							selectedMesageId: currentWinnerSelection?.selectedMesageId,
						}}
						setCurrentSelectedCard={setCurrentWinnerSelection}
						handleBlockSubmit={handleSubmitWinner}
					/>
				</Modal>
			)}
			<Formik
				initialValues={{ ...initialFormValues }}
				onSubmit={handleOnSubmit}
				validateOnChange
				validateOnMount={true}
				validate={(values: FormikValues): Promise<FormikErrors<FormikValues>> =>
					splitTestEnabled ? validateSplit(values) : validate(values)
				}
				enableReinitialize={true}
			>
				<SplitTestContextProvider
					{...{
						splitTestEnabled,
						setSplitTestEnabled,
						isFinalStatus,
						tabsList,
						setTabsList,
						selectedTabValue,
						setSelectedTabValue,
						initialIsSplit,
						setDeletedTabs,
						deletedTabs,
						currentWinnerSelection,
						setCurrentWinnerSelection,
					}}
					origMessageId={initialFormValues.messageId}
				>
					<Form>{children}</Form>
				</SplitTestContextProvider>
			</Formik>
		</Styled>
	);
};

export default SendEmail;
