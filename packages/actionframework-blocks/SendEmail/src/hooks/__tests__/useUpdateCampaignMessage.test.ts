import { renderQueryHook } from '@activecampaign/platform-core-test-helpers';
import { act } from '@testing-library/react-hooks';
import { useQueryClient } from '@tanstack/react-query';

import { updateCampaign } from '../../api/campaigns';
import { updateMessage } from '../../api/messages';
import {
	getCampaignCacheKey,
	getMessageByCampaignIdCacheKey,
	UpdateCampaignParams,
	UpdateMessageParams,
	useUpdateCampaignMessage,
} from '../../hooks';

jest.mock('@tanstack/react-query', () => {
	const originalModule = jest.requireActual('@tanstack/react-query');

	return {
		...originalModule,
		useQueryClient: jest.fn(),
	};
});
jest.mock('../../api/campaigns', () => ({
	updateCampaign: jest.fn(),
}));
jest.mock('../../api/messages', () => ({
	updateMessage: jest.fn(),
}));

describe('useUpdateCampaignMessage', () => {
	it('calls updateCampaign and updateMessage with expected parameters', async () => {
		const campaignId = 1;
		const messageId = 1;
		const subject = 'new subject';
		const preHeader = 'new preheader';
		const fromName = 'fromName';
		const fromEmail = 'fromEmail';
		const replyToEmail = 'replyToEmail';
		const name = 'new emailname';
		const replyTrackingEnabled = true;
		const googleAnalyticsLinkTrackingEnabled = true;
		const readTrackingEnabled = true;
		const linkTrackingEnabled = true;
		const updateCampaignParams: UpdateCampaignParams = {
			campaignId,
			name,
			replyTrackingEnabled,
			googleAnalyticsLinkTrackingEnabled,
			readTrackingEnabled,
			linkTrackingEnabled,
		};
		const updateMessageParams: UpdateMessageParams = {
			messageId,
			subject,
			preHeader,
			fromName,
			fromEmail,
			replyToEmail,
		};

		(useQueryClient as jest.Mock).mockReturnValue({
			invalidateQueries: jest.fn(),
		});

		(updateCampaign as jest.Mock).mockResolvedValueOnce({
			data: {
				id: campaignId,
			},
		});

		(updateMessage as jest.Mock).mockResolvedValueOnce({
			data: {
				id: messageId,
			},
		});

		const { result, waitFor } = renderQueryHook(() =>
			useUpdateCampaignMessage()
		);
		await act(async () => {
			await waitFor(() => {
				result.current.mutate({
					campaign: updateCampaignParams,
					message: updateMessageParams,
				});
			});
		});

		await waitFor(() => result.current.isSuccess);
		expect(updateCampaign).toHaveBeenCalledTimes(1);
		expect(updateCampaign).toHaveBeenCalledWith(campaignId, {
			name,
			replyTrackingEnabled,
			googleAnalyticsLinkTrackingEnabled,
			readTrackingEnabled,
			linkTrackingEnabled,
		});

		expect(updateMessage).toHaveBeenCalledTimes(1);
		expect(updateMessage).toHaveBeenCalledWith(messageId, {
			subject,
			preHeader,
			fromName,
			fromEmail,
			replyToEmail,
		});

		expect(useQueryClient().invalidateQueries).toHaveBeenCalledTimes(2);
		expect(useQueryClient().invalidateQueries).toHaveBeenCalledWith(
			getMessageByCampaignIdCacheKey(campaignId)
		);
		expect(useQueryClient().invalidateQueries).toHaveBeenCalledWith(
			getCampaignCacheKey(campaignId)
		);
	});
});
