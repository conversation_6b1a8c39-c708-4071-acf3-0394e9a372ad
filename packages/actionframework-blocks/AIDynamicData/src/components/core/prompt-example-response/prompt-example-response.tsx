import React, { useState } from 'react';
import Styled from '@activecampaign/camp-components-styled';
import { SemanticColors } from '@camp/tokens';
import Text from '@activecampaign/camp-components-text';
import { useTranslation } from '@activecampaign/core-translations-client';
import Button from '@activecampaign/camp-components-button';
import { AiLoadingCard } from '@activecampaign/camp-components-ai';
import {
	useAiDynamicDataPreview,
	useErrorMessageByStatus,
} from '../../../hooks';
import Flex from '@activecampaign/camp-components-flex';
import Link from '@activecampaign/camp-components-link';
import { Lightning } from '@activecampaign/camp-components-icon';
import { AIDynamicDataValues } from '../../../types';
import { useFormikContext } from 'formik';
import { queryClient } from '@activecampaign/platform-core-queries';
import { isGenericAction } from '../../../utils';

/**
 * This component is needed for old AI Dynamic Data, since it has a different API contract for preview
 * TODO: Delete and use ExampelResponse once the feature flag is enabled and archived
 */
export const PromptExampleResponse = (): JSX.Element => {
	const { t } = useTranslation();
	const { values } = useFormikContext<AIDynamicDataValues>();

	const [promptForQuery, setPromptForQuery] = useState('');
	const exampleResponse = useAiDynamicDataPreview(promptForQuery);
	const errorMessage = useErrorMessageByStatus(
		exampleResponse.error?.response.status,
		exampleResponse.error?.response.data
	);

	if (!isGenericAction(values) || !values.prompt) {
		return null;
	}

	const preview = exampleResponse.data?.preview;
	const showError = exampleResponse.isError;
	const showGetExampleButton =
		!(preview || showError) && !exampleResponse.isLoading;
	const showRefreshButton =
		(preview || showError) && !exampleResponse.isLoading;
	const showExamples = preview && !exampleResponse.isLoading;

	const fetchExampleResponse = (): void => {
		setPromptForQuery(values.prompt);
		setTimeout(() => {
			queryClient.invalidateQueries({
				queryKey: ['ai-dynamic-data-preview'],
			});
		});
	};

	return (
		<Styled mt="sp800" data-testid="ai-example-response">
			<Flex
				direction="row"
				justifyContent="space-between"
				mb="sp400"
				styles={{ width: '100%' }}
			>
				<Flex.Item>
					<Text size="fs200" weight="fwMedium" mb="sp600">
						{t('automations:actions:ai:example-response')}
					</Text>
				</Flex.Item>
				<Flex.Item>
					{showRefreshButton && (
						<Link
							size="fs200"
							onClick={fetchExampleResponse}
							styles={{ color: SemanticColors['text-primary'] }}
							data-testid="ai-example-response-refresh-btn"
						>
							{t('automations:actions:ai:refresh')}
						</Link>
					)}
				</Flex.Item>
			</Flex>
			{showGetExampleButton && (
				<Styled
					py="sp500"
					styles={{
						border: `1px ${SemanticColors['border-supportive']} solid`,
						borderRadius: 'radii200',
						width: '100%',
						textAlign: 'center',
					}}
				>
					<Button.Text
						onClick={fetchExampleResponse}
						data-testid="ai-example-response-get-btn"
						type="button"
					>
						{t('automations:actions:ai:get-example-response')}
					</Button.Text>
				</Styled>
			)}
			{exampleResponse.isLoading && (
				<AiLoadingCard
					data-testid="ai-example-response-loading"
					isLoading
					loadingIndicatorSize="large"
					styles={{
						width: '100%',
						height: '72px',
					}}
				/>
			)}
			{showExamples && (
				<>
					<Styled
						data-testid="ai-example-response-examples-content"
						p="sp500"
						mb="sp500"
						styles={{
							backgroundColor: SemanticColors['bg-informational'],
							borderRadius: 'radii200',
						}}
					>
						<Text size="fs200">{preview.generatedContent}</Text>
					</Styled>
					{preview.contact?.personalizedTags && (
						<>
							<Text as="p" my="sp400" size="fs200" weight="fwMedium">
								{t('automations:actions:ai:example-response-fields')}:
							</Text>
							{Object.entries(preview.contact.personalizedTags).map(
								([key, value]) => (
									<Flex key={key} alignItems="center" my="sp400">
										<Styled mr="sp100">
											<Lightning
												fill={SemanticColors['text-primary']}
												decorative={true}
											/>
										</Styled>
										<Text as="div" size="fs200">
											{key}: <Text weight="bold">{value}</Text>
										</Text>
									</Flex>
								)
							)}
						</>
					)}
				</>
			)}
			{showError && (
				<Styled
					data-testid="ai-example-response-error"
					p="sp500"
					styles={{
						backgroundColor: SemanticColors['bg-danger'],
						borderRadius: 'radii200',
					}}
				>
					<Text size="fs200">{errorMessage}</Text>
				</Styled>
			)}
		</Styled>
	);
};
