import { useTranslation } from '@activecampaign/core-translations-client';
import {
	InAppExpansion,
	UpgradeIntentions,
} from '@activecampaign/platform-components-in-app-expansion';
import {
	useGlobalData,
	useBillingAccess,
	useLocale,
	queryClient,
} from '@activecampaign/platform-core-queries';
import React from 'react';

type AiInteractionsUpgradeModalProps = {
	onClose: () => void;
};

export const AiInteractionsUpgradeModal: React.FC<
	AiInteractionsUpgradeModalProps
> = ({ onClose }) => {
	const { t } = useTranslation();

	const { data: globalData } = useGlobalData();

	const accountId = globalData?.acct?.account_id_old;

	const hasBillingAccess = useBillingAccess();
	const locale = useLocale();

	const handleOnSuccess = (): void => {
		queryClient.invalidateQueries({
			queryKey: ['ai-consumption'],
		});
		onClose();
	};

	const handleOnDismiss = (): void => {
		onClose();
	};

	return (
		<InAppExpansion
			accountId={accountId}
			intention={UpgradeIntentions.UPSELL_INCREASE_ACTIVITIES_LIMIT}
			locale={locale}
			handleDismiss={handleOnDismiss}
			onSuccess={handleOnSuccess}
			isAdmin={hasBillingAccess}
			aiFeatureBodyText={t('growth:in-app-expansion-modal:activities:body')}
		/>
	);
};
