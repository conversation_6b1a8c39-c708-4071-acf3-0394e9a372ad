import React, { useEffect, useMemo } from 'react';
import { AutomationAIDynamicDataBlockData } from '../types';
import Styled from '@activecampaign/camp-components-styled';
import {
	queryClient,
	useFeatureFlag,
} from '@activecampaign/platform-core-queries';
import { StepProvider } from '../context';
import { Form } from './form';
import { ActionStep } from '../types';
import { isGenericAction } from '../utils';

export type AIDynamicDataProps = {
	block?: AutomationAIDynamicDataBlockData;
	children?: JSX.Element;
	handleSubmit?: Function;
};

export const AIDynamicData: React.FC<AIDynamicDataProps> = ({
	block,
	children,
	handleSubmit,
}) => {
	const showPromptActions = useFeatureFlag('ai-prompt-action');

	const { params } = block;

	useEffect(() => {
		if (!!params?.fieldid && params?.fieldid !== -1) {
			queryClient.invalidateQueries({
				queryKey: ['infiniteContactFields'],
			});
		}
	}, [params?.fieldid]);

	// The "generic" action does not always have a type, so we need to check if the params have a prompt
	// This is to maintain backwards compatibility with existing AI Dynamic Data
	const initialStep = useMemo(() => {
		if (!showPromptActions || isGenericAction(params)) {
			return ActionStep.Generic;
		}

		return params.type ?? ActionStep.Selection;
	}, [params, showPromptActions]);

	return (
		<Styled className="c-AIDynamicData">
			<StepProvider initialStep={initialStep}>
				<Form block={block} handleSubmit={handleSubmit}>
					{children}
				</Form>
			</StepProvider>
		</Styled>
	);
};
