import { AxiosError, AxiosResponse } from 'axios';
import { useContext, useState } from 'react';
import {
	useMutation,
	useQuery,
	useQueryClient,
	UseMutationResult,
	UseQueryResult,
} from '@activecampaign/platform-core-queries';

import { useTranslation } from '@activecampaign/core-translations-client';
import { QueryToken } from '@activecampaign/platform-core-queries';

import {
	CreateAccountResponse,
	createAccount,
	deleteAccounts,
	getAccounts,
} from '@src/api/accounts';
import { associate } from '@src/api/utils';
import {
	flashErrorBanner,
	flashSuccessBanner,
	GlobalContext,
} from '@src/hooks';
import {
	Account,
	AccountsBulkDeleteResponse,
	AccountsMeta,
	AccountsResponse,
	FormattedAccountFieldGroup,
} from '@src/types';
import { parseHttpErrors } from '@src/utils/error';
import {
	AccountFieldsFormData,
	formatAccountRequestBody,
} from '@src/utils/fieldGroup';

import {
	ACCOUNT_CREATION_ID,
	ACCOUNT_CREATION_NAME,
	CREATION_SUCCESS_STORAGE_KEY,
} from '../config';
import { AccountListQuery } from '../types';
import { formatQueryParams, quantityLength } from '../utils';
import { useAccountData as useAccountFields } from '@activecampaign/platform-components-deals';

type UseAccountData = {
	accounts: UseQueryResult<{ accounts: Account[]; meta: AccountsMeta }>;
	accountFields: FormattedAccountFieldGroup[];
	createAccount: UseMutationResult<AxiosResponse<CreateAccountResponse>>;
	destroyAccounts: UseMutationResult<
		AxiosResponse<AccountsBulkDeleteResponse>,
		unknown,
		void
	>;
	isRefreshing: boolean;
	isTableDataLoading: boolean;
};

type Params = {
	onCloseDeleteModal: VoidFunction;
	onInitialized: VoidFunction;
	query: AccountListQuery;
	selectedAccounts: Account[];
};

// FIXME: As a temporary fix to not being able to reset fields in the account
// modal, when an account is created a local storage variable is saved and the
// page is reloaded. On load, if the variable is set the success modal will be
// shown. This method is used instead of adding the param to the URL, which
// will prevent unintended subsequent displays of the modal.
// The original code to be used is commented out with a note indicating that
// it should be restored when a fix for the modal is provided.

function useAccountData({
	onCloseDeleteModal,
	onInitialized,
	query,
	selectedAccounts,
}: Params): UseAccountData {
	const { dispatch } = useContext(GlobalContext);
	const [isRefreshing, setIsRefreshing] = useState(false);
	const queryClient = useQueryClient();
	const { t } = useTranslation();

	const { accountFields, fieldDataIsLoading } = useAccountFields();

	// Load account data when URL search string changes
	const accounts = useQuery(
		[QueryToken.Accounts, query],
		async () => {
			const { data } = await getAccounts(formatQueryParams(query));

			return {
				accounts: associate<AccountsResponse, Account[]>(data, 'accounts', {
					users: {
						id: 'owner',
						as: 'user',
					},
					customerAccountCustomFieldData: {
						id: 'accountCustomFieldData',
						as: 'customFieldData',
					},
				}),
				meta: data.meta,
			};
		},
		{
			keepPreviousData: true,
			onSuccess: onInitialized,
			refetchOnMount: 'always',
		}
	);
	// In order to avoid the Accounts table from loading the standard field and then the custom field columns
	// which cause a jarring effect, im adding the 'accountsFieldMeta' request loading state as part of
	// the 'accounts' request loading state which is already leveraged on the table component to determine
	// when to show the table data

	const isTableDataLoading = accounts.isFetching || fieldDataIsLoading;
	// Create a new account

	const createAccountMutation = useMutation(
		(fields: AccountFieldsFormData) => {
			return createAccount(formatAccountRequestBody(fields));
		},
		{
			onSuccess: (data) => {
				// Save local storage variable and reset page

				localStorage.setItem(ACCOUNT_CREATION_NAME, data.data.account.name);
				localStorage.setItem(ACCOUNT_CREATION_ID, data.data.account.id);
				localStorage.setItem(CREATION_SUCCESS_STORAGE_KEY, 'true');

				accounts.refetch();
				setIsRefreshing(true);
				window.location.reload();
			},
			onError: (err) => {
				dispatch(flashErrorBanner(parseHttpErrors(err as AxiosError)));
			},
		}
	);

	const destroyAccounts = useMutation(() => deleteAccounts(selectedAccounts), {
		// Configuration of callbacks
		onSuccess: () => {
			const quantity = quantityLength(selectedAccounts);
			const transString = t(`accounts.account-has-been-deleted.${quantity}`, {
				deletedAccountsCount: selectedAccounts.length,
			});

			dispatch(flashSuccessBanner(transString));
			onCloseDeleteModal();
			queryClient.invalidateQueries({
				queryKey: [QueryToken.Accounts],
			});
		},
		onError: () => {
			const quantity = quantityLength(selectedAccounts);
			const transString = t(
				`accounts.account-has-not-been-deleted.${quantity}`,
				{
					deletedAccountsCount: selectedAccounts.length,
				}
			);

			dispatch(flashErrorBanner(transString));
			onCloseDeleteModal();
		},
	});

	return {
		accounts,
		accountFields,
		createAccount: createAccountMutation,
		destroyAccounts,
		isRefreshing,
		isTableDataLoading,
	};
}

export default useAccountData;
