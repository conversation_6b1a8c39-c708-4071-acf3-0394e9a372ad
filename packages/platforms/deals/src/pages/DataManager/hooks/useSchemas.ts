import {
	useQuery,
	queryClient,
	QueryToken,
} from '@activecampaign/platform-core-queries';

import * as displayDataApi from '@src/api/customObjectsDisplayData';
import * as integrationsApi from '@src/api/integrationsApps';
import { SchemaWithIntegration } from '@src/types';

import { DISPLAY_PARAMS } from '../config';
import { formatSchemaFields, linkIntegrationToSchema } from '../utils/schemas';

type UseSchemas = {
	isFetched: boolean;
	schemas: SchemaWithIntegration[];
};

export default function useSchemas(): UseSchemas {
	// Fetch all schemas with integrations
	const { data: schemas, isFetched: isFetchedSchemas } = useQuery({
		queryKey: QueryToken.COSchemasWithIntegration,
		queryFn: getSchemasWithIntegrations,
		onSuccess: (schemas) => {
			// Save in query store to reduce additional network requests
			schemas.forEach((schema) => {
				queryClient.setQueryData(
					[QueryToken.COSchemaWithIntegration, schema.id],
					schema
				);
			});
		},
		placeholderData: [],
	});

	return {
		isFetched: isFetchedSchemas,
		schemas,
	};
}

async function getSchemasWithIntegrations(): Promise<SchemaWithIntegration[]> {
	const schemas = await displayDataApi
		.index(DISPLAY_PARAMS)
		.then((schemas) => schemas.map(formatSchemaFields));

	const appIds = schemas.map((schema) => schema.appId).filter(Boolean);
	const integrations = await integrationsApi.index(appIds);

	return schemas.map((schema) => linkIntegrationToSchema(schema, integrations));
}
