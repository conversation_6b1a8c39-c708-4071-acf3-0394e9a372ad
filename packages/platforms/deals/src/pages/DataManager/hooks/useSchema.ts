import { useQuery, QueryToken } from '@activecampaign/platform-core-queries';

import * as displayDataApi from '@src/api/customObjectsDisplayData';
import * as integrationsApi from '@src/api/integrationsApps';
import { SchemaWithIntegration } from '@src/types';

import { DISPLAY_PARAMS } from '../config';
import { formatSchemaFields, linkIntegrationToSchema } from '../utils/schemas';

type UseSchema = {
	isError: boolean;
	schema: SchemaWithIntegration;
};

export default function useSchema(id: string): UseSchema {
	const { data: schema, isError } = useQuery({
		queryKey: [QueryToken.COSchemaWithIntegration, id],
		queryFn: () => getSchemaWithIntegrations(id),
	});

	return {
		isError,
		schema,
	};
}

async function getSchemaWithIntegrations(
	id: string
): Promise<SchemaWithIntegration> {
	const schema = await displayDataApi
		.get(id, DISPLAY_PARAMS)
		.then(formatSchemaFields);

	if (!schema.appId) {
		return schema as SchemaWithIntegration;
	}

	const integrations = await integrationsApi.index([schema.appId]);

	return linkIntegrationToSchema(schema, integrations);
}
