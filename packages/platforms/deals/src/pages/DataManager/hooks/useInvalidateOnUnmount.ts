import { useEffect, useRef } from 'react';

import { queryClient, QueryToken } from '@activecampaign/platform-core-queries';

import { useSchema } from '../context/schema';
import { invalidateExternals } from '../utils/queries';

type QueueInvalidation = () => boolean;

export default function useInvalidateOnUnmount(): QueueInvalidation {
	const schema = useSchema();

	const idRef = useRef(null);
	const shouldInvalidateRef = useRef(false);

	useEffect(() => {
		idRef.current = schema?.id;
	}, [schema]);

	useEffect(() => {
		return (): void => {
			if (idRef.current && shouldInvalidateRef.current) {
				queryClient.invalidateQueries({
					queryKey: [QueryToken.COSchemaWithIntegration, idRef.current],
				});

				invalidateExternals(queryClient);
			}
		};
	}, []);

	return (): boolean => (shouldInvalidateRef.current = true);
}
