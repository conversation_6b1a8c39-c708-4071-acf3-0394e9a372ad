import React, { useEffect, useContext, useState } from 'react';
import { useForm } from 'react-hook-form';
import DOMPurify from 'dompurify';
import Loading from '@activecampaign/camp-components-loading-indicator';
import Modal from '@activecampaign/camp-components-modal';
import Styled from '@activecampaign/camp-components-styled';
import { createToast } from '@activecampaign/camp-components-toast';
import { useTranslation } from '@activecampaign/core-translations-client';
import Banner from '@activecampaign/camp-components-banner';
import Text from '@activecampaign/camp-components-text';
import {
	useMutation,
	useQueryClient,
} from '@activecampaign/platform-core-queries';
import { editWebhook, sendSampleData } from '@src/hooks';
import {
	EDIT_MODAL_TYPE,
	WEBHOOK_TYPE_STANDARD,
	WEBHOOKS_INITS,
	WEBHOOKS_EVENTS,
	WEBHOOK_TYPE_CUSTOM,
} from '@src/constants';
import { ModalFooter } from '../modal-footer';
import { AddEditForm } from '../add-edit-form';
import { Webhook } from '@src/types';
import { WebhooksContext } from '@src/context';
import { handleEventsListsFields, getErrors } from '@src/utils';

type EditProps = {
	resetModal: Function;
	toastSuccessText: string;
	activeWebhook: Webhook;
	activeWebhookCustom: boolean;
};

export const Edit: React.FC<EditProps> = ({
	resetModal,
	toastSuccessText,
	activeWebhook,
	activeWebhookCustom,
}) => {
	const queryClient = useQueryClient();
	const [sampleResult, setSampleResult] = useState<string>('');
	const { customWebhookEvents, standardWebhookEvents, lists } =
		useContext(WebhooksContext);
	const { t } = useTranslation();
	const addEditWebhookType = activeWebhookCustom
		? WEBHOOK_TYPE_CUSTOM
		: WEBHOOK_TYPE_STANDARD;

	const activeWebhookSources =
		activeWebhook &&
		activeWebhook.sources.map((source) => {
			const label = WEBHOOKS_INITS[source] || source;
			return { value: source, label };
		});

	const activeWebhookEvents =
		activeWebhook &&
		activeWebhook.events.map((event) => {
			if (addEditWebhookType === WEBHOOK_TYPE_CUSTOM) {
				const label = customWebhookEvents[event];
				return { value: event, label };
			} else {
				const label = WEBHOOKS_EVENTS[event] || event;
				return { value: event, label };
			}
		});

	const activeWebhookList =
		activeWebhook && lists.find((list) => list.id == activeWebhook.listid);

	const {
		control,
		handleSubmit,
		watch,
		formState: { errors },
		getValues,
		setValue,
		trigger,
	} = useForm({
		defaultValues: {
			name: activeWebhook.name,
			url: activeWebhook.url,
			events: activeWebhookEvents,
			init: activeWebhookSources,
			list: activeWebhookList?.id
				? {
						value: activeWebhookList?.id,
						label: `${activeWebhookList?.name} (${activeWebhookList?.id})`,
				  }
				: {
						value: '0',
						label: t('webhooks:all-lists-default-option'),
				  },
		},
	});

	watch('list');

	const webhookEvents =
		addEditWebhookType === WEBHOOK_TYPE_CUSTOM
			? customWebhookEvents
			: standardWebhookEvents;

	const eventsListsFields = handleEventsListsFields(
		getValues('list'),
		webhookEvents,
		addEditWebhookType
	);

	const noListRequired = addEditWebhookType === WEBHOOK_TYPE_CUSTOM;

	const {
		mutate: editWebhookMutate,
		isLoading: editWebhookLoading,
		isSuccess: editWebhookSuccess,
		error: editWebhookError,
		isError: editWebhookIsError,
	} = useMutation(() => {
		const sources =
			addEditWebhookType === WEBHOOK_TYPE_CUSTOM
				? ['custom_object']
				: getValues('init').map((obj) => obj.value);
		return editWebhook(activeWebhook.id, {
			name: DOMPurify.sanitize(getValues('name')),
			url: DOMPurify.sanitize(getValues('url')),
			events: getValues('events').map((obj) => obj.value),
			sources: sources,
			listid: noListRequired ? '0' : getValues('list.value').toString(),
			state: '1',
		});
	});

	useEffect(() => {
		if (editWebhookIsError) {
			const errors = getErrors(editWebhookError);
			if (errors?.length) {
				errors.forEach((error) =>
					createToast({
						title: t('webhooks:action-error'),
						appearance: 'danger',
						duration: 'long',
						dataTestId: 'webhooks-action-toast-error',
						description: error.title,
					})
				);
			}
		}
		if (editWebhookSuccess) {
			createToast({
				title: toastSuccessText,
				appearance: 'success',
				duration: 'standard',
				dataTestId: 'webhooks-action-toast-success',
			});
			resetModal();
			queryClient.invalidateQueries({
				queryKey: ['webhooks'],
			});
		}
	}, [
		editWebhookError,
		editWebhookIsError,
		editWebhookSuccess,
		resetModal,
		queryClient,
		toastSuccessText,
		t,
	]);

	/**
	 * kick off react query mutation via form submit
	 * @returns Promise {void}
	 */
	const submitForm = async (): Promise<void> => {
		await editWebhookMutate();
	};

	const testWebhook = async (): Promise<void> => {
		const url = DOMPurify.sanitize(getValues('url'));
		const events = getValues('events');
		await trigger(['url']);
		if (!errors?.url && events.length && url) {
			try {
				const response = await sendSampleData(
					url,
					events.map((event) => event.value)
				);
				setSampleResult(JSON.stringify(response?.data, null, 4));
				createToast({
					title: t('webhooks:webhook-modal:sample-sent-toast'),
					appearance: 'success',
					duration: 'short',
					dataTestId: 'webhooks-sample-toast-success',
				});
			} catch (error) {
				console.error('error', error);
			}
		}
	};

	return (
		<Styled
			onSubmit={handleSubmit(() => submitForm())}
			as="form"
			data-testid={`${EDIT_MODAL_TYPE}-webhook-modal`}
			onKeyDown={(e): void => {
				if (e.key === 'Enter' && e.target.type !== 'submit') {
					e.preventDefault();
				}
			}}
		>
			<Modal.Body styles={{ maxHeight: 'calc(100vh - 250px)' }}>
				{activeWebhook.state === '0' && (
					<Banner
						data-testid={'webhook-modal-deactivated'}
						appearance="danger"
						description={t(
							'webhooks:webhook-modal:banner-deactivated-description'
						)}
						title={t('webhooks:webhook-modal:banner-deactivated-title')}
					/>
				)}
				<AddEditForm
					setValue={setValue}
					control={control}
					errors={errors}
					type={EDIT_MODAL_TYPE}
					lists={lists}
					webhokType={addEditWebhookType}
					eventOptions={eventsListsFields.eventOptions}
					listHelperText={t(`${eventsListsFields.listHelperText}`)}
				/>
				{sampleResult && (
					<Styled
						styles={{ borderTop: 'b100 bSolid slate200' }}
						pt={'sp400'}
						mt={'sp600'}
					>
						<Text.Heading as="h3" height="lh300" size="fs300" mb={'sp200'}>
							{t('webhooks:webhook-modal:sample-body-header')}
						</Text.Heading>
						<pre
							style={{
								overflowX: 'auto',
								maxHeight: '300px',
							}}
						>
							{sampleResult}
						</pre>
					</Styled>
				)}
			</Modal.Body>
			<ModalFooter
				showSubmitBtn={true}
				showBackBtn={false}
				showCancelBtn={
					addEditWebhookType === WEBHOOK_TYPE_CUSTOM ? false : true
				}
				resetModal={testWebhook}
				cancelDisabled={
					!!(
						addEditWebhookType === WEBHOOK_TYPE_CUSTOM ||
						!(getValues('events').length > 0 && getValues('url').length > 0)
					)
				}
				submitDisabled={false}
				cancelText={t('webhooks:webhook-modal:sample-data-btn')}
				submitText={
					editWebhookLoading ? (
						<Loading
							data-testid="edit-modal-action-submitting"
							appearance="inverse"
						/>
					) : (
						t('global:update.translation')
					)
				}
				type={EDIT_MODAL_TYPE}
			/>
		</Styled>
	);
};
