import React, { useState, useEffect, useContext } from 'react';
import { useForm } from 'react-hook-form';
import DOMPurify from 'dompurify';
import Loading from '@activecampaign/camp-components-loading-indicator';
import Modal from '@activecampaign/camp-components-modal';
import Styled from '@activecampaign/camp-components-styled';
import { createToast } from '@activecampaign/camp-components-toast';
import { useTranslation } from '@activecampaign/core-translations-client';
import Text from '@activecampaign/camp-components-text';
import {
	useMutation,
	useQueryClient,
} from '@activecampaign/platform-core-queries';
import { addWebhook, sendSampleData } from '@src/hooks';
import {
	ADD_MODAL_TYPE,
	WEBHOOK_STANDARD,
	WEBHOOK_TYPE_STANDARD,
	WEBHOOK_CUSTOM,
	WEBHOOK_TYPE_CUSTOM,
} from '@src/constants';
import { AddWebhookType } from '@src/components';
import { ModalFooter } from '../modal-footer';
import { AddEditForm } from '../add-edit-form';
import { AddEditWebhookTypes } from '@src/types';
import styles from './add.styles';
import { WebhooksContext } from '@src/context';
import { handleEventsListsFields, getErrors } from '@src/utils';

type AddProps = {
	resetModal: Function;
	toastSuccessText: string;
	handleModalTitle: Function;
};

export const Add: React.FC<AddProps> = ({
	resetModal,
	toastSuccessText,
	handleModalTitle,
}) => {
	const queryClient = useQueryClient();
	const [sampleResult, setSampleResult] = useState<string>('');
	const { customWebhookEvents, standardWebhookEvents, lists } =
		useContext(WebhooksContext);
	const { t } = useTranslation();
	const [addEditWebhookType, setAddEditWebhookType] =
		useState<AddEditWebhookTypes>(null);
	const DEFAULT_LIST = {
		value: '0',
		label: t('webhooks:all-lists-default-option'),
	};
	const selectedAddWebhookType =
		addEditWebhookType && addEditWebhookType.length > 1 ? true : false;
	const {
		control,
		handleSubmit,
		watch,
		formState: { errors, dirtyFields },
		getValues,
		resetField,
		setValue,
		trigger,
	} = useForm({
		defaultValues: {
			list: DEFAULT_LIST,
			init: [],
			events: [],
			name: '',
			url: '',
		},
	});

	watch('list');

	const webhookEvents =
		addEditWebhookType === WEBHOOK_TYPE_CUSTOM
			? customWebhookEvents
			: standardWebhookEvents;

	const eventsListsFields = handleEventsListsFields(
		getValues('list'),
		webhookEvents,
		addEditWebhookType
	);

	const noListRequired = addEditWebhookType === WEBHOOK_TYPE_CUSTOM;
	const {
		mutate: addWebhookMutate,
		isLoading: addWebhookLoading,
		isSuccess: addWebhookSuccess,
		error: addWebhookError,
		isError: addWebhookIsError,
	} = useMutation(() => {
		const sources =
			addEditWebhookType === WEBHOOK_TYPE_CUSTOM
				? ['custom_object']
				: getValues('init').map((obj) => obj.value);
		return addWebhook({
			name: DOMPurify.sanitize(getValues('name')),
			url: DOMPurify.sanitize(getValues('url')),
			events: getValues('events').map((obj) => obj.value),
			sources: sources,
			listid: noListRequired ? '0' : getValues('list.value').toString(),
		});
	});

	useEffect(() => {
		if (addWebhookIsError) {
			const errors = getErrors(addWebhookError);
			if (errors?.length) {
				errors.forEach((error) =>
					createToast({
						title: t('webhooks:action-error'),
						appearance: 'danger',
						duration: 'long',
						dataTestId: 'webhooks-action-toast-error',
						description: error.title,
					})
				);
			}
		}
		if (addWebhookSuccess) {
			createToast({
				title: toastSuccessText,
				appearance: 'success',
				duration: 'standard',
				dataTestId: 'webhooks-action-toast-success',
			});
			resetModal();
			queryClient.invalidateQueries({
				queryKey: ['webhooks'],
			});
		}
	}, [
		addWebhookIsError,
		addWebhookError,
		addWebhookSuccess,
		resetModal,
		queryClient,
		toastSuccessText,
		t,
	]);

	function backModal(): void {
		handleModalTitle(t('webhooks:webhook-modal:back-title'));
		resetField('list');
		resetField('init');
		resetField('events');
		resetField('url');
		setAddEditWebhookType(null);
	}

	/**
	 * kick off react query mutation via form submit
	 * @returns Promise {void}
	 */
	const submitForm = async (): Promise<void> => {
		await addWebhookMutate();
	};

	const testWebhook = async (): Promise<void> => {
		const url = DOMPurify.sanitize(getValues('url'));
		const events = getValues('events');
		await trigger(['url']);
		if (!errors?.url && events.length && url) {
			try {
				const response = await sendSampleData(
					url,
					events.map((event) => event.value)
				);
				setSampleResult(JSON.stringify(response?.data, null, 4));
				createToast({
					title: t('webhooks:webhook-modal:sample-sent-toast'),
					appearance: 'success',
					duration: 'short',
					dataTestId: 'webhooks-sample-toast-success',
				});
			} catch (error) {
				console.error('error', error);
			}
		}
	};

	return (
		<div>
			<Styled
				onSubmit={handleSubmit(() => submitForm())}
				as="form"
				data-testid={`${ADD_MODAL_TYPE}-webhook-modal`}
				onKeyDown={(e): void => {
					if (e.key === 'Enter' && e.target.type !== 'submit') {
						e.preventDefault();
					}
				}}
			>
				<Modal.Body styles={{ maxHeight: 'calc(100vh - 250px)' }}>
					{!selectedAddWebhookType && (
						<>
							<Styled styles={styles.selectWebhookType}>
								<AddWebhookType
									onClick={(): void => {
										handleModalTitle(
											t('webhooks:webhook-modal:standard-title-add')
										);
										setAddEditWebhookType(WEBHOOK_TYPE_STANDARD);
									}}
									webhookType={WEBHOOK_STANDARD}
								/>
							</Styled>
							<AddWebhookType
								onClick={(): void => {
									handleModalTitle(
										t('webhooks:webhook-modal:custom-title-add')
									);
									setAddEditWebhookType(WEBHOOK_TYPE_CUSTOM);
								}}
								webhookType={WEBHOOK_CUSTOM}
							/>
						</>
					)}
					{selectedAddWebhookType && (
						<AddEditForm
							setValue={setValue}
							control={control}
							errors={errors}
							type={ADD_MODAL_TYPE}
							lists={lists}
							webhokType={addEditWebhookType}
							eventOptions={eventsListsFields.eventOptions}
							listHelperText={t(`${eventsListsFields.listHelperText}`)}
						/>
					)}
					{sampleResult && (
						<Styled
							styles={{ borderTop: 'b100 bSolid slate200' }}
							pt={'sp400'}
							mt={'sp600'}
						>
							<Text.Heading as="h3" height="lh300" size="fs300" mb={'sp200'}>
								{t('webhooks:webhook-modal:sample-body-header')}
							</Text.Heading>
							<pre
								style={{
									overflowX: 'auto',
									maxHeight: '300px',
								}}
							>
								{sampleResult}
							</pre>
						</Styled>
					)}
				</Modal.Body>
				<ModalFooter
					showSubmitBtn={selectedAddWebhookType}
					showBackBtn={selectedAddWebhookType}
					showCancelBtn={
						addEditWebhookType === WEBHOOK_TYPE_CUSTOM ? false : true
					}
					resetModal={selectedAddWebhookType ? testWebhook : resetModal}
					backModal={backModal}
					cancelDisabled={
						!!(
							addEditWebhookType === WEBHOOK_TYPE_CUSTOM ||
							(selectedAddWebhookType &&
								!(
									getValues('events').length > 0 && getValues('url').length > 0
								))
						)
					}
					submitDisabled={
						selectedAddWebhookType &&
						((addEditWebhookType === WEBHOOK_TYPE_STANDARD &&
							Object.keys(dirtyFields).length < 4) ||
							(addEditWebhookType === WEBHOOK_TYPE_CUSTOM &&
								Object.keys(dirtyFields).length !== 3))
							? true
							: false
					}
					cancelText={
						selectedAddWebhookType
							? t('webhooks:webhook-modal:sample-data-btn')
							: t('global:cancel.translation')
					}
					submitText={
						addWebhookLoading ? (
							<Loading
								data-testid="add-modal-action-submitting"
								appearance="inverse"
							/>
						) : (
							t('global:save.translation')
						)
					}
					type={ADD_MODAL_TYPE}
				/>
			</Styled>
		</div>
	);
};
