import React, { useContext, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import Loading from '@activecampaign/camp-components-loading-indicator';
import Modal from '@activecampaign/camp-components-modal';
import Styled from '@activecampaign/camp-components-styled';
import Text from '@activecampaign/camp-components-text';
import { createToast } from '@activecampaign/camp-components-toast';
import { useTranslation } from '@activecampaign/core-translations-client';
import {
	useMutation,
	useQueryClient,
} from '@activecampaign/platform-core-queries';
import { deleteWebhook } from '@src/hooks';
import { DELETE_MODAL_TYPE, ORIGIN_TOOLBAR } from '@src/constants';
import { WebhooksContext } from '@src/context';
import { ModalFooter } from '../modal-footer';

type DeleteProps = {
	resetModal: Function;
};

export const Delete: React.FC<DeleteProps> = ({ resetModal }) => {
	const {
		activeWebhook,
		deleteModalOrigin,
		selectedWebhooks,
		setActiveWebhook,
		setDeleteModalOrigin,
		setSelectedWebhooks,
	} = useContext(WebhooksContext);
	const { handleSubmit } = useForm();
	const queryClient = useQueryClient();
	const { t } = useTranslation();
	const multipleSelectedWebhooks =
		deleteModalOrigin === ORIGIN_TOOLBAR && selectedWebhooks.length > 1
			? true
			: false;

	const { mutate, isLoading, isSuccess, isError } = useMutation(async () => {
		if (deleteModalOrigin === ORIGIN_TOOLBAR) {
			const deletePromises = selectedWebhooks.map((webhook) =>
				deleteWebhook(webhook.id)
			);
			return Promise.all(deletePromises);
		} else {
			return deleteWebhook(activeWebhook.id);
		}
	});

	/**
	 * kick off delete Webhook(s) mutation
	 * @returns Promise {void}
	 */
	const submitForm = async (): Promise<void> => {
		await mutate();
	};

	useEffect(() => {
		if (isError) {
			createToast({
				title: t('webhooks:action-error.translation'),
				appearance: 'danger',
				duration: 'short',
				dataTestId: 'webhooks-action-toast-error',
			});
		}

		if (isSuccess) {
			createToast({
				title: multipleSelectedWebhooks
					? t('webhooks:webhook-deleted.translation.other')
					: t('webhooks:webhook-deleted.translation.one'),
				appearance: 'success',
				duration: 'short',
				dataTestId: 'webhooks-action-toast-success',
			});
			deleteModalOrigin === ORIGIN_TOOLBAR
				? setSelectedWebhooks([])
				: setActiveWebhook(null);
			setDeleteModalOrigin(null);
			resetModal();
			queryClient.invalidateQueries({
				queryKey: ['webhooks'],
			});
		}
	}, [
		deleteModalOrigin,
		isError,
		isLoading,
		isSuccess,
		multipleSelectedWebhooks,
		queryClient,
		resetModal,
		setActiveWebhook,
		setDeleteModalOrigin,
		setSelectedWebhooks,
		t,
	]);

	return (
		<Styled
			as="form"
			data-testid="delete-webhook-modal"
			onSubmit={handleSubmit(() => submitForm())}
		>
			<Modal.Body styles={{ maxHeight: 'calc(100vh - 250px)' }}>
				<Text mb="sp400" size="fs200" family="ffStandard" height="lh200">
					{multipleSelectedWebhooks
						? t('webhooks:delete-webhook-disclaimer-text.translation.other')
						: t('webhooks:delete-webhook-disclaimer-text.translation.one', {
								webhookName:
									deleteModalOrigin === ORIGIN_TOOLBAR
										? selectedWebhooks[0]?.name
										: activeWebhook?.name,
						  })}
				</Text>
			</Modal.Body>
			<ModalFooter
				showBackBtn={false}
				showSubmitBtn={true}
				showCancelBtn={true}
				cancelDisabled={false}
				resetModal={resetModal}
				submitDisabled={false}
				submitText={
					isLoading ? (
						<Loading
							data-testid="webhook-action-submitting"
							appearance="inverse"
						/>
					) : multipleSelectedWebhooks ? (
						t('webhooks:delete-webhook.translation.other')
					) : (
						t('webhooks:delete-webhook.translation.one')
					)
				}
				type={DELETE_MODAL_TYPE}
			/>
		</Styled>
	);
};
