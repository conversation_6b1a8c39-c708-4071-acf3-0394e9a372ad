import { useQuery } from '@activecampaign/platform-core-queries';
import {
	OnboardingResponse,
	ConnectedAppsResponse,
	AutomationsResponse,
} from '@src/types';
import { AxiosPromise } from 'axios';
import { apiService } from '@src/utils';

/**
 * Get connected apps info
 *
 * @param id
 */
export const getConnectedApps = (): AxiosPromise => {
	return apiService('get', `/api/3/connections`, {});
};

export const useConnectedApps = (): ConnectedAppsResponse => {
	const result = useQuery(
		['connectedApps'],
		async () => {
			const { data } = await getConnectedApps();
			// Filter connections down to only active connections
			if (data?.connections) {
				data.connections = data.connections.filter((o) => o.status === '1');
			}
			return data;
		},
		{
			refetchIntervalInBackground: true,
			refetchOnWindowFocus: true,
			// refetchInterval: 60000, // poll every min
			refetchInterval: 10000, // poll 10 sec
		}
	);

	const { isError, isFetched, isLoading, refetch, isFetching, isRefetching } =
		result;
	const data = result.data || [];

	return {
		data,
		isError,
		isFetched,
		isLoading,
		isRefetching,
		refetch,
		isFetching,
	};
};

/**
 * Get onboarding info
 *
 * @param id
 */
export const getOnboardingLog = (): AxiosPromise => {
	return apiService('get', `/api/3/account/onboardingLog`, {});
};

export const useOnboarding = (): OnboardingResponse => {
	const result = useQuery({
		queryKey: ['onboardingLog'],
		queryFn: async () => {
			const { data } = await getOnboardingLog();
			return data;
		},
	});

	const { isError, isFetched, isLoading, refetch, isFetching, isRefetching } =
		result;
	const data = result.data || [];

	return {
		data,
		isError,
		isFetched,
		isLoading,
		isRefetching,
		refetch,
		isFetching,
	};
};

/**
 * Get automation recipe info
 *
 * @param id
 */
export const importRecipe = (tplShareHost: string): AxiosPromise => {
	const tplHashes = [
		// 'NHqAgb6', // local test
		'gUIcefS', // abandon cart
	];
	return apiService('post', '/admin/api.php?jq=1&f=series.series_import', {
		params: {
			url: `http://${tplShareHost}/${tplHashes[0]}`,
			source: 'ecom-onboarding',
		},
	});
};

/**
 * Get automations
 *
 * @param id
 */
export const getAllAutomations = (): AxiosPromise => {
	return apiService('get', `/api/3/automations?limit=0`, {});
};

export const useAutomations = (): AutomationsResponse => {
	const result = useQuery({
		queryKey: ['automationsData'],
		queryFn: async () => {
			const { data } = await getAllAutomations();
			return data;
		},
	});

	const { isError, isFetched, isLoading, refetch, isFetching, isRefetching } =
		result;
	const data = result.data || [];

	return {
		data,
		isError,
		isFetched,
		isLoading,
		isRefetching,
		refetch,
		isFetching,
	};
};
