import { useQuery } from '@activecampaign/platform-core-queries';
import {
	IntegrationDetailErrorsResponse,
	IntegrationDetailOptionsResponse,
	IntegrationDetailResponse,
	IntegrationSyncStatusResponse,
} from '@src/types';
import { AxiosPromise } from 'axios';
import { apiService } from '@src/utils';
import {
	SYNC_STATUS_STOPPED,
	SYNC_STATUS_RUNNING,
	INTEGRATION_TYPE_ECOMMERCE,
	INTEGRATION_TYPE_FACEBOOK,
} from '@src/constants';

/**
 * edit a Webhook
 *
 * @parama id {number} id of list to edit
 * @param formData {Record<string, boolean>}
 */
export const editConnectionOptions = (
	id: number | unknown,
	type: string,
	value: string | unknown,
	optionType?: string
): AxiosPromise => {
	if (type === INTEGRATION_TYPE_ECOMMERCE) {
		return apiService('put', `/api/3/connectionOptions/${id}`, {
			data: {
				connectionOption: {
					option: optionType,
					value,
				},
			},
		});
	}
	if (type === INTEGRATION_TYPE_FACEBOOK) {
		return apiService('put', `/api/3/connectionOptions/${id}`, {
			data: {
				connectionOption: { option: 'facebook_audience.enabled', value },
			},
		});
	}
};

/**
 * edit a Webhook
 *
 * @parama id {number} id of list to edit
 * @param formData {Record<string, boolean>}
 */
export const createConnectionOptions = (
	connectionId: number | unknown,
	type: string,
	minPageViewTime: number | unknown,
	sessionTimeout: number | unknown,
	productUrlPatterns: string | unknown
): Promise<unknown[]> => {
	if (type === INTEGRATION_TYPE_ECOMMERCE) {
		// TODO: we will replace this with a single call to the API once we have the API ready to handle all the options
		// will be done in task https://activecampaign.atlassian.net/browse/ECMM-2468
		return Promise.all([
			apiService('post', `/api/3/connectionOptions`, {
				data: {
					connectionOption: {
						connectionid: connectionId,
						option: 'browse_abandonment.minimum_page_view_time',
						value: minPageViewTime,
					},
				},
			}),
			apiService('post', `/api/3/connectionOptions`, {
				data: {
					connectionOption: {
						connectionid: connectionId,
						option: 'browse_abandonment.session_timeout',
						value: sessionTimeout,
					},
				},
			}),
			apiService('post', `/api/3/connectionOptions`, {
				data: {
					connectionOption: {
						connectionid: connectionId,
						option: 'browse_abandonment.product_url_patterns',
						value: productUrlPatterns,
					},
				},
			}),
		]);
	}
};

/**
 * Get campaign summary from id
 *
 * @param id
 */
export const getIntegrationDetailOptions = (
	id: number | string
): AxiosPromise => {
	return apiService('get', `/api/3/connections/${id}/options`, {});
};

/**
 * Get integration sync status from id
 *
 * @param id
 */
export const getIntegrationSyncStatus = (id: number | string): AxiosPromise => {
	return apiService('get', `/api/3/connections/${id}/syncStatus`, {});
};

export const useIntegrationSyncStatus = (
	id: number | string
): IntegrationSyncStatusResponse => {
	const result = useQuery({
		queryKey: ['integrationSyncStatus'],
		queryFn: async () => {
			if (!id) {
				return;
			}
			const { data } = await getIntegrationSyncStatus(id);
			return data;
		},
		refetchIntervalInBackground: true,
		refetchOnWindowFocus: true,
		refetchInterval: 2000, // poll every 2 seconds
	});

	const { isError, isFetched, isLoading, refetch, isFetching, isRefetching } =
		result;
	const data = result.data || [];

	return {
		data,
		isError,
		isFetched,
		isLoading,
		isRefetching,
		refetch,
		isFetching,
	};
};

/**
 * Get integration summary from id
 *
 * @param id
 */
export const getIntegrationDetail = (id: number | string): AxiosPromise => {
	return apiService('get', `/api/3/connections/${id}`, {});
};

export const useIntegrationDetail = (
	id: number | string
): IntegrationDetailResponse => {
	const result = useQuery({
		queryKey: ['integrationDetail'],
		queryFn: async () => {
			if (!id) {
				return;
			}
			const { data } = await getIntegrationDetail(id);
			return data;
		},
		refetchIntervalInBackground: false,
		refetchOnWindowFocus: true,
		refetchInterval: 15000, // poll 15 seconds
	});

	const { isError, isFetched, isLoading, refetch, isFetching, isRefetching } =
		result;
	const data = result.data || [];

	return {
		data,
		isError,
		isFetched,
		isLoading,
		isRefetching,
		refetch,
		isFetching,
	};
};

/**
 * Get campaign summary from id
 *
 * @param id
 */
export const getIntegrationDetailErrors = (
	id: number | string
): AxiosPromise => {
	return apiService(
		'get',
		`/api/3/connection/errorLog?connection=${id}&limit=50`,
		{}
	);
};

export const useIntegrationDetailErrors = (
	id: number | string
): IntegrationDetailErrorsResponse => {
	const result = useQuery({
		queryKey: ['integrationDetailErrors'],
		queryFn: async () => {
			if (!id) {
				return;
			}
			const { data } = await getIntegrationDetailErrors(id);
			return data;
		},
		refetchIntervalInBackground: true,
		refetchOnWindowFocus: true,
		refetchInterval: 60000, // poll every min
	});

	const { isError, isFetched, isLoading, refetch, isFetching, isRefetching } =
		result;
	const data = result.data || [];

	return {
		data,
		isError,
		isFetched,
		isLoading,
		isRefetching,
		refetch,
		isFetching,
	};
};

export const useIntegrationDetailOptions = (
	id: number | string
): IntegrationDetailOptionsResponse => {
	const result = useQuery({
		queryKey: ['integrationDetailOptions'],
		queryFn: async () => {
			if (!id) {
				return;
			}
			const { data } = await getIntegrationDetailOptions(id);
			return data;
		},
	});

	const { isError, isFetched, isLoading, refetch, isFetching } = result;
	const data = result.data || [];

	return {
		data,
		isError,
		isFetched,
		isLoading,
		refetch,
		isFetching,
	};
};

/**
 * Sync integration
 *
 * @param id {number | string}
 */
export const syncIntegration = (id: number | string): AxiosPromise => {
	return apiService('put', `/api/3/connections/${id}`, {
		data: {
			connection: { syncStatus: SYNC_STATUS_RUNNING },
		},
	});
};

export const resetSyncIntegration = (id: number | string): AxiosPromise => {
	return apiService('put', `/api/3/connections/${id}`, {
		data: {
			connection: { syncStatus: SYNC_STATUS_STOPPED },
		},
	});
};

export const historicSyncIntegration = (id: number | string): AxiosPromise => {
	return apiService('put', `/api/3/connections/${id}`, {
		data: {
			connection: { syncStatus: SYNC_STATUS_RUNNING, lastSync: null },
		},
	});
};

export const deleteIntegration = (id: number | string): AxiosPromise => {
	return apiService('delete', `/api/3/connections/${id}`, {});
};

/**
 * Sync integration
 *
 * @param id {number | string}
 */
export const sendConnect = (
	service: string,
	externalid: string
): AxiosPromise => {
	return apiService(
		'post',
		`/api/3/connection/oauth?service=${service}&externalid=${externalid}`,
		{
			data: {
				service,
				externalid,
			},
		}
	);
};
