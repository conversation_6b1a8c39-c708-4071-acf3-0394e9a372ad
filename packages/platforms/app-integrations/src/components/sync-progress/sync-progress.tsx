import React, { useEffect, useState } from 'react';
import Styled from '@activecampaign/camp-components-styled';
import styles from './sync-progress.styles';
import Text from '@activecampaign/camp-components-text';
import Flex from '@activecampaign/camp-components-flex';
import LoadingIndicator from '@activecampaign/camp-components-loading-indicator';
import { useIntegrationSyncStatus, resetSyncIntegration } from '@src/hooks';
import { Integration, Entitlements } from '@src/types';
import { hasEntitlement } from '@src/utils';
import { check, clockTime } from '@activecampaign/camp-tokens-icon';
import { Icon } from '@activecampaign/camp-components-icon';
import { useQueryClient } from '@activecampaign/platform-core-queries';
import { createToast } from '@activecampaign/camp-components-toast';
import { useTranslation } from '@activecampaign/core-translations-client';

type SyncProgressProps = {
	integration: Integration;
	isVisible: boolean;
	entitlements: Entitlements;
};

type StatusProps = {
	label: string;
};

type SyncStatusProps = {
	status: number;
	label: string;
};

export const SyncProgress: React.FC<SyncProgressProps> = ({
	integration,
	isVisible,
	entitlements,
}) => {
	const { t } = useTranslation();
	const queryClient = useQueryClient();
	const [syncStatus, setSyncStatus] = useState(null);
	const [percentage, setPercentage] = useState('0');
	const [hasShownToast, setHasShownToast] = useState(false);
	const hasProductCatalogEntitlement =
		hasEntitlement(
			entitlements?.fetchedEntitlements,
			entitlements?.isErrorEntitlements,
			entitlements?.entitlementsData,
			['ecom-product-catalog']
		) ||
		hasEntitlement(
			entitlements?.fetchedEntitlements,
			entitlements?.isErrorEntitlements,
			entitlements?.entitlementsData,
			['ecommerce']
		);
	const {
		data: integrationSyncStatusData,
		isError,
		isFetched,
		isLoading,
	} = useIntegrationSyncStatus(integration?.id);

	useEffect(() => {
		if (isFetched && integrationSyncStatusData) {
			if (integrationSyncStatusData?.data) {
				setSyncStatus(integrationSyncStatusData?.data);
				const keysToCheck = [
					'customersImportState',
					'productsImportState',
					'ordersImportState',
					'contactSyncState',
					'ecomDataSyncState',
				];
				const countOf2 = keysToCheck.reduce(
					(count, key) =>
						count + (integrationSyncStatusData?.data[key] === 2 ? 1 : 0),
					0
				);
				setPercentage(((countOf2 / keysToCheck.length) * 100).toString());
				if (percentage === '100') {
					setTimeout(() => {
						queryClient.invalidateQueries({
							queryKey: ['connectedApps'],
						});
						queryClient.invalidateQueries({
							queryKey: ['integrationDetail'],
						});
					}, 2000);
				}
			}
		}
		if (
			isError ||
			(isFetched &&
				integrationSyncStatusData &&
				!integrationSyncStatusData?.data &&
				!hasShownToast)
		) {
			createToast({
				title: t('integrations:detail-view:toast-error'),
				appearance: 'danger',
				duration: 'short',
				dataTestId: 'integrations-option-toast-error-api',
			});
			resetSyncIntegration(integration?.id);
			setHasShownToast(true);
		}
	}, [
		hasShownToast,
		integration,
		t,
		integrationSyncStatusData,
		percentage,
		queryClient,
		isError,
		isFetched,
		isLoading,
	]);

	// Reset state when component becomes visible
	useEffect(() => {
		if (isVisible) {
			setSyncStatus(null);
			setPercentage('0');
			setHasShownToast(false);
			queryClient.invalidateQueries({
				queryKey: ['connectedApps'],
			});
			queryClient.invalidateQueries({
				queryKey: ['integrationSyncStatus'],
			});
		}
	}, [isVisible, queryClient]);

	// Separate useEffect hook for invalidating queries
	useEffect(() => {
		if (
			isError ||
			(isFetched &&
				integrationSyncStatusData &&
				!integrationSyncStatusData?.data)
		) {
			queryClient.invalidateQueries({
				queryKey: ['connectedApps'],
			});
			queryClient.invalidateQueries({
				queryKey: ['integrationDetail'],
			});
		}
	}, [isError, isFetched, integrationSyncStatusData, queryClient]);

	const NotStartedSync: React.FC<StatusProps> = ({ label }) => (
		<>
			<Icon
				mr="sp300"
				data-testid="activity-detail-task-title-icon"
				fill="slate300"
				decorative
				use={clockTime}
				styles={styles.progressBarIcon}
			/>
			<Text.Body as="p" weight="fwMedium" color="slate300">
				{label}
			</Text.Body>
		</>
	);

	const ActiveSync: React.FC<StatusProps> = ({ label }) => (
		<>
			<Styled mr="sp300">
				<LoadingIndicator appearance="default" size="small" />
			</Styled>
			<Text.Body as="p" weight="fwMedium" color="slate600">
				{label}
			</Text.Body>
		</>
	);

	const FinishedSync: React.FC<StatusProps> = ({ label }) => (
		<>
			<Icon
				mr="sp300"
				data-testid="activity-detail-task-title-icon"
				fill="mint600"
				decorative
				use={check}
				styles={styles.progressBarIcon}
			/>
			<Text.Body as="p" weight="fwMedium" color="mint600">
				{label}
			</Text.Body>
		</>
	);

	const SyncStatus: React.FC<SyncStatusProps> = ({
		status,
		label,
	}: SyncStatusProps) => (
		<Flex alignItems="center" mt="sp300">
			{(status === 0 || !status) && <NotStartedSync label={label} />}
			{status === 1 && <ActiveSync label={label} />}
			{status === 2 && <FinishedSync label={label} />}
		</Flex>
	);

	return (
		<>
			<Flex justifyContent="space-between">
				<Text.Body weight="fwSemiBold">{t('integrations:syncing')}</Text.Body>
				<Text.Body weight="fwSemiBold">{percentage}%</Text.Body>
			</Flex>
			<Styled as="div" mt="sp300" styles={styles.progressBar(percentage)} />
			<SyncStatus
				status={syncStatus?.customersImportState}
				label={t('integrations:detail-view:sync-progress-customers')}
			/>
			{hasProductCatalogEntitlement && entitlements?.fetchedEntitlements && (
				<SyncStatus
					status={syncStatus?.productsImportState}
					label={t('integrations:detail-view:sync-progress-products')}
				/>
			)}
			{!hasProductCatalogEntitlement && entitlements?.fetchedEntitlements && (
				<SyncStatus
					status={syncStatus?.productsImportState}
					label={t(
						'integrations:detail-view:sync-progress-products-not-entitled'
					)}
				/>
			)}
			<SyncStatus
				status={syncStatus?.ordersImportState}
				label={t('integrations:detail-view:sync-progress-orders')}
			/>
			<SyncStatus
				status={syncStatus?.contactSyncState}
				label={t('integrations:detail-view:sync-progress-sync-customers')}
			/>
			<SyncStatus
				status={syncStatus?.ecomDataSyncState}
				label={t('integrations:detail-view:sync-progress-sync-orders')}
			/>
		</>
	);
};
