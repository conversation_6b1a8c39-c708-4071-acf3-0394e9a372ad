import React, {
	useEffect,
	useState,
	useMemo,
	useCallback,
	useRef,
} from 'react';
import Styled from '@activecampaign/camp-components-styled';
import styles from './manage-options.styles';
import Dropdown from '@activecampaign/camp-components-dropdown';
import LoadingIndicator from '@activecampaign/camp-components-loading-indicator';
import Accordion from '@activecampaign/camp-components-accordion';
import Button from '@activecampaign/camp-components-button';
import Checkbox from '@activecampaign/camp-components-checkbox';
import Banner from '@activecampaign/camp-components-banner';
import Tooltip from '@activecampaign/camp-components-tooltip';
import Text from '@activecampaign/camp-components-text';
import Flex from '@activecampaign/camp-components-flex';
import {
	InAppExpansion,
	UpgradeIntentions,
} from '@activecampaign/platform-components-in-app-expansion';
import moment from 'moment';
import {
	useIntegrationDetailOptions,
	editConnectionOptions,
	createConnectionOptions,
	historicSyncIntegration,
	syncIntegration,
} from '@src/hooks';
import { useTranslation } from '@activecampaign/core-translations-client';
import { Integration, Entitlements, GlobalData } from '@src/types';
import { SyncProgress } from '@src/components';
import {
	INTEGRATION_TYPES,
	INTEGRATION_TYPE_FACEBOOK,
	INTEGRATION_TYPE_SHOPIFY,
	INTEGRATION_TYPE_ECOMMERCE,
} from '@src/constants';
import { createToast } from '@activecampaign/camp-components-toast';
import { HelperText, Label } from '@activecampaign/camp-core-form-control';
import { useDateFormatters } from '@activecampaign/platform-core-queries';
import {
	useMutation,
	useQueryClient,
	useBillingAccess,
} from '@activecampaign/platform-core-queries';
import {
	isConnectionError,
	isSyncInProgress,
	isThirdParty,
	canManage,
	canSync,
	hasEntitlement,
	hasBrowseAbandonmentEnabled,
} from '@src/utils';
import Textarea from '@activecampaign/camp-components-textarea';

type ManageOptionsProps = {
	integration: Integration;
	entitlements: Entitlements;
	globalData: GlobalData;
};

export const ManageOptions: React.FC<ManageOptionsProps> = ({
	integration,
	entitlements,
	globalData,
}) => {
	const queryClient = useQueryClient();
	const hasBillingAccess = useBillingAccess();
	const type =
		INTEGRATION_TYPES[integration?.service]?.type || INTEGRATION_TYPE_ECOMMERCE;
	const { formatDatetime } = useDateFormatters();
	const { t } = useTranslation();
	const ABCART_OPTIONS = useMemo(
		() => ({
			type: 'abandoned_cart.abandon_after_hours',
			selectOptions: [
				{
					key: 1,
					value: `1 ${t('time:hour')} ${t(
						'integrations:ecommerce:abandoned-cart:recommended'
					)}`,
				},
				{ key: 6, value: `6 ${t('time:hours')}` },
				{ key: 10, value: `10 ${t('time:hours')}` },
				{ key: 24, value: `24 ${t('time:hours')}` },
			],
		}),
		[t]
	);
	const [selectedABCartSetting, setABCartSetting] = useState(
		ABCART_OPTIONS.selectOptions[0].value
	);
	const COBRA_CONSIDERATION_OPTIONS = useMemo(
		() => ({
			type: 'browse_abandonment.minimum_page_view_time',
			selectOptions: [
				{ key: 8, value: `8 ${t('time:seconds')}` },
				{
					key: 10,
					value: `10 ${t('time:seconds')} ${t(
						'integrations:ecommerce:abandoned-cart:recommended'
					)}`,
				},
				{ key: 30, value: `30 ${t('time:seconds')}` },
			],
			default: 10,
		}),
		[t]
	);
	const [selectedCobraConsiderationSetting, setCobraConsiderationSetting] =
		useState(COBRA_CONSIDERATION_OPTIONS.selectOptions[1].value);

	const COBRA_TIMEOUT_OPTIONS = useMemo(
		() => ({
			type: 'browse_abandonment.session_timeout',
			selectOptions: [
				{ key: 60, value: `1 ${t('time:hour')}` },
				{
					key: 180,
					value: `3 ${t('time:hours')} ${t(
						'integrations:ecommerce:abandoned-cart:recommended'
					)}`,
				},
				{ key: 480, value: `8 ${t('time:hours')}` },
				{ key: 1440, value: `24 ${t('time:hours')}` },
			],
			default: 180,
		}),
		[t]
	);
	const [selectedCobraTimeoutSetting, setCobraTimeoutSetting] = useState(
		COBRA_TIMEOUT_OPTIONS.selectOptions[1].value
	);
	const COBRA_VALID_URL_VARIABLES = useMemo(
		() => [
			'sku',
			'storePrimaryId',
			'storeBaseProductId',
			'upc',
			'baseProductUrlSlug',
			'variantProductUrlSlug',
		],
		[]
	);
	const [customUrlSetting, setCustomUrlSetting] = useState('');
	const [validCustomUrlSetting, setValidCustomUrlSetting] = useState(true);
	const [urlPatternError, setUrlPatternError] = useState(null);
	const [textareaRows, setTextareaRows] = useState(1);
	const previousCustomUrlSetting = useRef('');
	const [showInAppExpansionModal, setShowInAppExpansionModal] = useState(false);
	const [selectedSettingID, setSelectedSettingID] = useState(null);
	const [settingsDisabled, setSettingsDisabled] = useState(false);
	const [fbookAudienceEnabled, setFbookAudienceEnabled] = useState(false);
	const [showSyncProgress, setShowSyncProgress] = useState(false);
	const { data: integrationOptions, isFetching } = useIntegrationDetailOptions(
		integration?.id
	);
	const browseAbandonmentUrlSetting =
		integrationOptions?.connectionOptions?.find(
			(item) => item.option === 'browse_abandonment.product_url_patterns'
		);
	const hasValidUrlPattern =
		browseAbandonmentUrlSetting?.value &&
		JSON.parse(browseAbandonmentUrlSetting.value || '[]').length > 0;
	const cobraDropdownDisabled =
		isFetching || settingsDisabled || !hasValidUrlPattern;
	const syncInProgress = isSyncInProgress(integration);
	const hasSyncEntitlement =
		hasEntitlement(
			entitlements?.fetchedEntitlements,
			entitlements?.isErrorEntitlements,
			entitlements?.entitlementsData,
			['ecom-historical-sync']
		) ||
		hasEntitlement(
			entitlements?.fetchedEntitlements,
			entitlements?.isErrorEntitlements,
			entitlements?.entitlementsData,
			['ecommerce']
		);

	const hasBrowseAbandonmentEntitlement = hasEntitlement(
		entitlements?.fetchedEntitlements,
		entitlements?.isErrorEntitlements,
		entitlements?.entitlementsData,
		['ecom-browse-abandonment']
	);
	const calculateTextAreaRows = (text: string): number => {
		const minRows = 1;
		const maxRows = 12;
		const lines = text.split('\n').length;
		return Math.max(minRows, Math.min(maxRows, lines));
	};

	useEffect(() => {
		setShowSyncProgress(syncInProgress);
	}, [syncInProgress]);

	// Reset ref when integration changes
	useEffect(() => {
		previousCustomUrlSetting.current = '';
	}, [integration?.id]);

	useEffect(() => {
		if (
			integrationOptions?.connectionOptions &&
			integrationOptions.connectionOptions.length > 0
		) {
			if (type === INTEGRATION_TYPE_ECOMMERCE) {
				const optionsToCheck = [
					'abandoned_cart.abandon_after_hours',
					'browse_abandonment.minimum_page_view_time',
					'browse_abandonment.session_timeout',
					'browse_abandonment.product_url_patterns',
				];

				const settings = integrationOptions.connectionOptions.filter((item) =>
					optionsToCheck.includes(item.option)
				);
				settings.forEach((setting) => {
					if (setting && setting?.value) {
						if (setting.option === 'abandoned_cart.abandon_after_hours') {
							const newDefault = ABCART_OPTIONS.selectOptions.find(
								(item) => item.key.toString() === setting.value
							);
							setABCartSetting(newDefault.value);
						} else if (
							setting.option === 'browse_abandonment.minimum_page_view_time'
						) {
							const newDefault = COBRA_CONSIDERATION_OPTIONS.selectOptions.find(
								(item) => item.key.toString() === setting.value
							);
							setCobraConsiderationSetting(newDefault.value);
						} else if (
							setting.option === 'browse_abandonment.session_timeout'
						) {
							const newDefault = COBRA_TIMEOUT_OPTIONS.selectOptions.find(
								(item) => item.key.toString() === setting.value
							);
							setCobraTimeoutSetting(newDefault.value);
						} else if (
							setting.option === 'browse_abandonment.product_url_patterns'
						) {
							const parsedValue = JSON.parse(setting.value);
							// Convert array to semicolon + newline separated string for display
							const displayValue = Array.isArray(parsedValue)
								? parsedValue.join(';\n')
								: parsedValue;
							setCustomUrlSetting(displayValue);
							// Set textarea rows based on content
							setTextareaRows(calculateTextAreaRows(displayValue));
							// Initialize the ref with the loaded value
							previousCustomUrlSetting.current = displayValue;
						}
					}
				});
			}
			if (type === INTEGRATION_TYPE_FACEBOOK) {
				const fbookAudience = integrationOptions.connectionOptions.find(
					(item) => item.option === 'facebook_audience.enabled'
				);
				if (fbookAudience) {
					setSelectedSettingID(fbookAudience.id);
					if (fbookAudience?.value === '1') {
						setFbookAudienceEnabled(true);
					} else {
						setFbookAudienceEnabled(false);
					}
				}
			}
		}
	}, [
		ABCART_OPTIONS,
		COBRA_CONSIDERATION_OPTIONS,
		COBRA_TIMEOUT_OPTIONS,
		integrationOptions,
		type,
	]);

	const {
		mutate: syncIntegrationOption,
		isLoading: syncIntegrationOptionLoading,
		isSuccess: syncIntegrationOptionSuccess,
		isError: syncIntegrationOptionError,
	} = useMutation({
		mutationFn: ({
			value,
			optionType,
		}: {
			value: string;
			optionType?: string;
		}) => editConnectionOptions(selectedSettingID, type, value, optionType),
	});

	const {
		mutate: enableBrowseAbandonmentOptions,
		isLoading: createIntegrationOptionsLoading,
		isSuccess: createIntegrationOptionsSuccess,
		isError: createIntegrationOptionsOptionError,
	} = useMutation({
		mutationFn: ({ value }: { value: string }) =>
			createConnectionOptions(
				integration.id,
				type,
				COBRA_CONSIDERATION_OPTIONS.default,
				COBRA_TIMEOUT_OPTIONS.default,
				value
			),
		// Prevent multiple calls by using a mutation key
		mutationKey: ['createIntegrationOptions', integration.id],
	});

	useEffect(() => {
		setSettingsDisabled(false);
		if (syncIntegrationOptionLoading || createIntegrationOptionsLoading) {
			setSettingsDisabled(true);
		}
		if (syncIntegrationOptionError || createIntegrationOptionsOptionError) {
			createToast({
				title: t('integrations:detail-view:toast-error'),
				appearance: 'danger',
				duration: 'short',
				dataTestId: 'integrations-option-toast-error-api',
			});
		}
		if (syncIntegrationOptionSuccess || createIntegrationOptionsSuccess) {
			createToast({
				title: t('integrations:detail-view:toast-success'),
				appearance: 'success',
				duration: 'short',
				dataTestId: 'integrations-option-toast-success',
			});
			// Invalidate and refetch the integration options
			queryClient.invalidateQueries({
				queryKey: ['integrationDetailOptions'],
			});
			queryClient.invalidateQueries({
				queryKey: ['integrationDetail'],
			});
		}
	}, [
		syncIntegrationOptionLoading,
		syncIntegrationOptionSuccess,
		syncIntegrationOptionError,
		createIntegrationOptionsLoading,
		createIntegrationOptionsSuccess,
		createIntegrationOptionsOptionError,
		queryClient,
		t,
	]);

	function handleSelectSettingID(options): void {
		if (integrationOptions?.connectionOptions) {
			if (type === INTEGRATION_TYPE_ECOMMERCE) {
				if (options) {
					const setting = integrationOptions.connectionOptions.find(
						(item) => item.option === options.type
					);
					if (setting) {
						setSelectedSettingID(setting.id);
					}
				}
			}
		}
	}

	async function handleSelect(value, options = null): Promise<void> {
		if (selectedSettingID) {
			if (type === INTEGRATION_TYPE_ECOMMERCE && options) {
				if (options.type === 'abandoned_cart.abandon_after_hours') {
					setABCartSetting(value);
				} else if (
					options.type === 'browse_abandonment.minimum_page_view_time'
				) {
					setCobraConsiderationSetting(value);
				} else if (options.type === 'browse_abandonment.session_timeout') {
					setCobraTimeoutSetting(value);
				}

				const realVal = options.selectOptions.find(
					(item) => item.value.toString() === value
				);
				await syncIntegrationOption({
					value: realVal.key.toString(),
					optionType: options.type,
				});
			}
			if (type === INTEGRATION_TYPE_FACEBOOK) {
				setFbookAudienceEnabled(value);
				await syncIntegrationOption({ value: value });
			}
		} else {
			createToast({
				title: t('integrations:detail-view:toast-error'),
				appearance: 'danger',
				duration: 'short',
				dataTestId: 'integrations-option-toast-error-optionid',
			});
		}
	}

	// This function is to validate the whole URL pattern structure and check http or https protocol
	const isValidURL = (url): boolean => {
		const pattern = /^(https?:\/\/[\w.-]+)(?:\/.*)?(?:\?.*)?$/;
		return pattern.test(url);
	};

	// https://my-store.shop/**/products/{{sku}}
	const validProductUrlPattern = (patterns): boolean => {
		const validUrlPatternVariables = [
			'sku',
			'storePrimaryId',
			'storeBaseProductId',
			'upc',
			'baseProductUrlSlug',
			'variantProductUrlSlug',
		];

		const regexp = /\{\{(.*?)}}/g;

		// Handle empty input
		if ('' === patterns) {
			setUrlPatternError(null);
			return true;
		}

		// Split by semicolon and trim whitespace (handle newlines)
		const patternArray = patterns
			.split(';')
			.map((pattern) => pattern.replace(/\n/g, '').trim())
			.filter((pattern) => pattern.length > 0);

		// Check for duplicate patterns
		const uniquePatterns = new Set(patternArray);
		if (uniquePatterns.size !== patternArray.length) {
			// Find the first duplicate
			const seen = new Set();
			for (let i = 0; i < patternArray.length; i++) {
				const pattern = patternArray[i];
				if (seen.has(pattern)) {
					setUrlPatternError(
						t(
							'integrations:ecommerce:browse-abandoned-custom-product-url:duplicate-url-pattern'
						) +
							' ' +
							t(
								'integrations:ecommerce:browse-abandoned-custom-product-url:invalid-url-pattern',
								{
									patternNumber: i + 1,
									pattern: pattern,
								}
							)
					);
					return false;
				}
				seen.add(pattern);
			}
		}

		// Validate each pattern
		for (let i = 0; i < patternArray.length; i++) {
			const pattern = patternArray[i];

			// URL structure validation
			if (!isValidURL(pattern)) {
				setUrlPatternError(
					t(
						'integrations:ecommerce:browse-abandoned-custom-product-url:invalid-url'
					) +
						' ' +
						t(
							'integrations:ecommerce:browse-abandoned-custom-product-url:invalid-url-pattern',
							{
								patternNumber: i + 1,
								pattern: pattern,
							}
						)
				);
				return false;
			}

			// Wildcard validation
			//https://my-store.shop/**/products/**/slug/collection/**/corner
			if (pattern.split('**').length > 3) {
				setUrlPatternError(
					t(
						'integrations:ecommerce:browse-abandoned-custom-product-url:invalid-wildcard-count'
					) +
						' ' +
						t(
							'integrations:ecommerce:browse-abandoned-custom-product-url:invalid-url-pattern',
							{
								patternNumber: i + 1,
								pattern: pattern,
							}
						)
				);
				return false;
			}

			// Variable validations
			// https://my-store.shop/**/products/{{sku}}/{{id}}
			// https://my-store.shop/**/products/{{id}}
			const matches = [...pattern.matchAll(regexp)];
			if (1 !== matches.length) {
				setUrlPatternError(
					`Invalid URL pattern: exactly one variable is required (Pattern ${
						i + 1
					}: ${pattern})`
				);
				return false;
			} else {
				const variable = matches[0][1];
				if (!validUrlPatternVariables.includes(variable)) {
					setUrlPatternError(
						t(
							'integrations:ecommerce:browse-abandoned-custom-product-url:invalid-variable-value',
							{
								invalidValue: variable,
								validValues: COBRA_VALID_URL_VARIABLES.join(', '),
							}
						) +
							' ' +
							t(
								'integrations:ecommerce:browse-abandoned-custom-product-url:invalid-url-pattern',
								{
									patternNumber: i + 1,
									pattern: pattern,
								}
							)
					);
					return false;
				}
			}

			// Wildcard next To Variable Validation
			// https://example.com/**{{sku}}
			if (pattern.includes('**{{') || pattern.includes('}}**')) {
				setUrlPatternError(
					t(
						'integrations:ecommerce:browse-abandoned-custom-product-url:invalid-wildcard-position'
					) +
						' ' +
						t(
							'integrations:ecommerce:browse-abandoned-custom-product-url:invalid-url-pattern',
							{
								patternNumber: i + 1,
								pattern: pattern,
							}
						)
				);
				return false;
			}
		}

		setUrlPatternError(null);
		return true;
	};

	const onChangeCustomURL = (e): void => {
		let value = e.target.value;

		// Auto-format: add newline after semicolon if not already present
		if (value.endsWith(';') && !value.endsWith(';\n')) {
			value = value + '\n';
		}

		setCustomUrlSetting(value);

		// Auto-resize textarea based on content
		setTextareaRows(calculateTextAreaRows(value));

		// For validation, we still use the semicolon-separated format
		const trimmedValue = value.trim();
		if (validProductUrlPattern(trimmedValue)) {
			setValidCustomUrlSetting(true);
		} else {
			setValidCustomUrlSetting(false);
		}
	};

	const handleCustomUrlInputBlurOrEnter =
		useCallback(async (): Promise<void> => {
			// Prevent multiple calls if already processing
			if (createIntegrationOptionsLoading || syncIntegrationOptionLoading) {
				return;
			}

			if (!validCustomUrlSetting) {
				return;
			}

			if (
				integrationOptions?.connectionOptions &&
				integrationOptions.connectionOptions.length > 0
			) {
				if (type === INTEGRATION_TYPE_ECOMMERCE) {
					const setting = integrationOptions.connectionOptions.find(
						(item) => item.option === 'browse_abandonment.product_url_patterns'
					);
					if (setting) {
						// Only update if the value has actually changed
						if (customUrlSetting !== previousCustomUrlSetting.current) {
							previousCustomUrlSetting.current = customUrlSetting;
							const normalizedValue =
								!customUrlSetting || customUrlSetting.trim() === ''
									? null
									: customUrlSetting;

							// Convert semicolon-separated string to array (strip newlines)
							const urlArray = normalizedValue
								? customUrlSetting
										.split(';')
										.map((url) => url.replace(/\n/g, '').trim())
										.filter((url) => url.length > 0)
								: [];

							await syncIntegrationOption({
								value: urlArray.length > 0 ? JSON.stringify(urlArray) : null,
								optionType: 'browse_abandonment.product_url_patterns',
							});
						}
					}
				}
			} else {
				// Only create if no connection options exist and we have a valid URL and it has changed
				if (
					type === INTEGRATION_TYPE_ECOMMERCE &&
					customUrlSetting.length > 0 &&
					customUrlSetting !== previousCustomUrlSetting.current
				) {
					previousCustomUrlSetting.current = customUrlSetting;
					// Convert semicolon-separated string to array (strip newlines)
					const urlArray = customUrlSetting
						.split(';')
						.map((url) => url.replace(/\n/g, '').trim())
						.filter((url) => url.length > 0);
					await enableBrowseAbandonmentOptions({
						value: JSON.stringify(urlArray),
					});
				}
			}
		}, [
			createIntegrationOptionsLoading,
			syncIntegrationOptionLoading,
			validCustomUrlSetting,
			customUrlSetting,
			integrationOptions?.connectionOptions,
			type,
			syncIntegrationOption,
			enableBrowseAbandonmentOptions,
		]);

	const {
		mutate: syncIntegrationMutate,
		isLoading: syncIntegrationLoading,
		isSuccess: syncIntegrationSuccess,
		isError: syncIntegrationError,
	} = useMutation(() => syncIntegration(integration?.id));

	useEffect(() => {
		if (syncIntegrationSuccess) {
			queryClient.invalidateQueries({
				queryKey: ['integrationDetail'],
			});
			createToast({
				title: t('integrations:detail-view:manual-sync-started-success'),
				appearance: 'success',
				duration: 'short',
				dataTestId: 'integrations-sync-toast-success',
			});
		}
		if (syncIntegrationError) {
			createToast({
				title: t('integrations:detail-view:toast-error'),
				appearance: 'danger',
				duration: 'short',
				description: t('integrations:errors:unexpected_error:title'),
				dataTestId: 'integrations-sync-toast-error-api',
			});
		}
	}, [
		syncIntegrationLoading,
		syncIntegrationSuccess,
		syncIntegrationError,
		queryClient,
		t,
	]);

	const {
		mutate: historicSyncIntegrationMutate,
		isLoading: historicSyncIntegrationMutateLoading,
		isSuccess: historicSyncIntegrationMutateSuccess,
		isError: historicSyncIntegrationMutateError,
	} = useMutation(() => historicSyncIntegration(integration?.id));

	useEffect(() => {
		if (historicSyncIntegrationMutateSuccess) {
			queryClient.invalidateQueries({
				queryKey: ['integrationDetail'],
			});
			createToast({
				title: t('integrations:detail-view:sync-started-success'),
				appearance: 'success',
				duration: 'short',
				dataTestId: 'integrations-sync-toast-success',
			});
		}
		if (historicSyncIntegrationMutateError) {
			createToast({
				title: t('integrations:detail-view:toast-error'),
				appearance: 'danger',
				duration: 'short',
				description: t('integrations:errors:unexpected_error:title'),
				dataTestId: 'integrations-sync-toast-error-api',
			});
		}
	}, [
		historicSyncIntegrationMutateLoading,
		historicSyncIntegrationMutateSuccess,
		historicSyncIntegrationMutateError,
		queryClient,
		t,
	]);

	const upsellTooltipBtn = (): React.ReactNode => {
		return (
			<Button.Text
				size="small"
				onClick={(): void => setShowInAppExpansionModal(true)}
				data-testid="tooltip-button"
			>
				{t('global:upgrade')}
			</Button.Text>
		);
	};

	const upsellTooltipContent = (): React.ReactNode => {
		return (
			<Flex
				direction="column"
				wrap="wrap"
				styles={{
					alignItems: 'center',
					justifyContent: 'center',
				}}
			>
				<Text as="p" p="sp200" family="ffStandard" height="lh200" size="fs200">
					{t('integrations:upsell-tooltip-text')}
				</Text>
			</Flex>
		);
	};

	return (
		<Styled as="div" styles={styles.detailHeader} data-testid="manage-options">
			{isConnectionError(integration) && (
				<Styled mb="sp600">
					<Banner
						appearance="danger"
						description={t(
							'integrations:status:error-external-account-description',
							{
								service: integration.serviceName,
							}
						)}
						title={t('integrations:status:error')}
					/>
				</Styled>
			)}
			{(type === INTEGRATION_TYPE_FACEBOOK ||
				(type === INTEGRATION_TYPE_ECOMMERCE &&
					canManage(integration.service)) ||
				(type === INTEGRATION_TYPE_ECOMMERCE &&
					hasBrowseAbandonmentEnabled(integration.service))) && (
				<Styled mb="sp600">
					<Accordion title={t('h:settings')}>
						<Styled p="sp400">
							{type === INTEGRATION_TYPE_FACEBOOK && (
								<>
									<div>
										<Label mb="sp200">
											{t('integrations:detail-view:facebook-enabled-switch')}
										</Label>
									</div>
									<Checkbox
										disabled={isConnectionError(integration)}
										checked={fbookAudienceEnabled}
										appearance="toggle"
										label={
											fbookAudienceEnabled
												? t(
														'integrations:detail-view:facebook-enabled-switch-enabled'
												  )
												: t(
														'integrations:detail-view:facebook-enabled-switch-eisabled'
												  )
										}
										onChange={handleSelect}
										value="fbookAudienceEnabled"
									/>
								</>
							)}
							{type === INTEGRATION_TYPE_ECOMMERCE &&
								canManage(integration.service) && (
									<>
										<Dropdown
											appearance="inline"
											dataTestId="ab-cart-settings"
											label={t('integrations:ecommerce:abandoned-cart:label')}
											labelPosition="top"
											required
											disabled={isFetching || settingsDisabled}
											onClick={(): void =>
												handleSelectSettingID(ABCART_OPTIONS)
											}
											onSelect={(value): Promise<void> =>
												handleSelect(value, ABCART_OPTIONS)
											}
											popoverPlacement="bottom"
											options={ABCART_OPTIONS.selectOptions.map(({ value }) =>
												value.toString()
											)}
											optionToString={(option): string => option.toString()}
											popoverTestId="ab-cart-settings-popover"
											selected={selectedABCartSetting.toString()}
											styles={{ width: 'max-content' }}
											triggerTestId="ab-cart-settings-trigger"
										/>
										<HelperText mt="sp200">
											{t('integrations:ecommerce:abandoned-cart:description')}
										</HelperText>
									</>
								)}
							{isThirdParty(integration) && (
								<>
									<Text
										as="p"
										family="ffStandard"
										weight="fwMedium"
										height="lh200"
										size="fs200"
									>
										{t('integrations:third-party-managed-nolink', {
											service: integration.serviceName,
										})}
									</Text>
								</>
							)}
							<>
								{type === INTEGRATION_TYPE_ECOMMERCE &&
									hasBrowseAbandonmentEnabled(integration.service) && (
										<Styled mt="sp400">
											{!hasBrowseAbandonmentEntitlement && (
												<Styled styles={{ display: 'inline-block' }}>
													<Tooltip
														placement="top"
														content={upsellTooltipContent()}
														contentHeavy
														contentHeavyAction={upsellTooltipBtn}
													>
														<Dropdown
															appearance="inline"
															dataTestId="browse-abandoned-consideration-settings"
															label={t(
																'integrations:ecommerce:browse-abandoned-consideration:label'
															)}
															labelPosition="top"
															required
															disabled={true}
															onClick={(): void =>
																handleSelectSettingID(
																	COBRA_CONSIDERATION_OPTIONS
																)
															}
															onSelect={(value): Promise<void> =>
																handleSelect(value, COBRA_CONSIDERATION_OPTIONS)
															}
															popoverPlacement="bottom"
															options={COBRA_CONSIDERATION_OPTIONS.selectOptions.map(
																({ value }) => value.toString()
															)}
															optionToString={(option): string =>
																option.toString()
															}
															popoverTestId="cobra-consideration-settings-popover"
															selected={selectedCobraConsiderationSetting.toString()}
															styles={{ width: 'max-content' }}
															triggerTestId="browse-abandoned-consideration-settings-trigger"
														/>
													</Tooltip>
												</Styled>
											)}
											{hasBrowseAbandonmentEntitlement && (
												<Dropdown
													appearance="inline"
													dataTestId="browse-abandoned-consideration-settings"
													label={t(
														'integrations:ecommerce:browse-abandoned-consideration:label'
													)}
													labelPosition="top"
													required
													disabled={cobraDropdownDisabled}
													onClick={(): void =>
														handleSelectSettingID(COBRA_CONSIDERATION_OPTIONS)
													}
													onSelect={(value): Promise<void> =>
														handleSelect(value, COBRA_CONSIDERATION_OPTIONS)
													}
													popoverPlacement="bottom"
													options={COBRA_CONSIDERATION_OPTIONS.selectOptions.map(
														({ value }) => value.toString()
													)}
													optionToString={(option): string => option.toString()}
													popoverTestId="cobra-consideration-settings-popover"
													selected={selectedCobraConsiderationSetting.toString()}
													styles={{ width: 'max-content' }}
													triggerTestId="browse-abandoned-consideration-settings-trigger"
												/>
											)}
											<HelperText mt="sp200">
												{t(
													'integrations:ecommerce:browse-abandoned-consideration:description'
												)}
											</HelperText>
										</Styled>
									)}
								{type === INTEGRATION_TYPE_ECOMMERCE &&
									hasBrowseAbandonmentEnabled(integration.service) && (
										<Styled mt="sp400">
											{!hasBrowseAbandonmentEntitlement && (
												<Styled styles={{ display: 'inline-block' }}>
													<Tooltip
														placement="top"
														content={upsellTooltipContent()}
														contentHeavy
														contentHeavyAction={upsellTooltipBtn}
													>
														<Dropdown
															appearance="inline"
															dataTestId="browse-abandoned-timeout-settings"
															label={t(
																'integrations:ecommerce:browse-abandoned-timeout:label'
															)}
															labelPosition="top"
															required
															disabled={true}
															onClick={(): void =>
																handleSelectSettingID(COBRA_TIMEOUT_OPTIONS)
															}
															onSelect={(value): Promise<void> =>
																handleSelect(value, COBRA_TIMEOUT_OPTIONS)
															}
															popoverPlacement="bottom"
															options={COBRA_TIMEOUT_OPTIONS.selectOptions.map(
																({ value }) => value.toString()
															)}
															optionToString={(option): string =>
																option.toString()
															}
															popoverTestId="cobra-timeout-settings-popover"
															selected={selectedCobraTimeoutSetting.toString()}
															styles={{ width: 'max-content' }}
															triggerTestId="browse-abandoned-timeout-settings-trigger"
														/>
													</Tooltip>
												</Styled>
											)}
											{hasBrowseAbandonmentEntitlement && (
												<Dropdown
													appearance="inline"
													dataTestId="browse-abandoned-timeout-settings"
													label={t(
														'integrations:ecommerce:browse-abandoned-timeout:label'
													)}
													labelPosition="top"
													required
													disabled={cobraDropdownDisabled}
													onClick={(): void =>
														handleSelectSettingID(COBRA_TIMEOUT_OPTIONS)
													}
													onSelect={(value): Promise<void> =>
														handleSelect(value, COBRA_TIMEOUT_OPTIONS)
													}
													popoverPlacement="bottom"
													options={COBRA_TIMEOUT_OPTIONS.selectOptions.map(
														({ value }) => value.toString()
													)}
													optionToString={(option): string => option.toString()}
													popoverTestId="cobra-timeout-settings-popover"
													selected={selectedCobraTimeoutSetting.toString()}
													styles={{ width: 'max-content' }}
													triggerTestId="browse-abandoned-timeout-settings-trigger"
												/>
											)}

											<HelperText mt="sp200">
												{t(
													'integrations:ecommerce:browse-abandoned-timeout:description'
												)}
											</HelperText>
										</Styled>
									)}
								{type === INTEGRATION_TYPE_ECOMMERCE &&
									hasBrowseAbandonmentEnabled(integration.service) && (
										<Styled mt="sp400">
											{!hasBrowseAbandonmentEntitlement && (
												<Styled styles={{ display: 'inline-block' }}>
													<Tooltip
														placement="top"
														content={upsellTooltipContent()}
														contentHeavy
														contentHeavyAction={upsellTooltipBtn}
													>
														<Textarea
															appearance="inline"
															data-testid="browse-abandoned-custom-product-url-settings"
															label={t(
																'integrations:ecommerce:browse-abandoned-custom-product-url:label'
															)}
															placeholder={t(
																'integrations:ecommerce:browse-abandoned-custom-product-url:text-area-placeholder'
															)}
															required
															disabled={true}
															rows={textareaRows}
															hasMinHeight={false}
															onChange={(e): void => onChangeCustomURL(e)}
															onFocus={(): void =>
																handleSelectSettingID({
																	type: 'browse_abandonment.product_url_patterns',
																})
															}
															onKeyDown={(e): Promise<void> =>
																e.key === 'Enter' &&
																handleCustomUrlInputBlurOrEnter()
															}
															onBlur={(): Promise<void> =>
																handleCustomUrlInputBlurOrEnter()
															}
															invalid={!validCustomUrlSetting}
															helperText={urlPatternError || null}
															value={customUrlSetting}
															styles={{ width: '600px' }}
															fieldStyles={{
																resize: 'vertical',
																minHeight: `${textareaRows * 1.5}rem`,
																height: 'auto',
															}}
														/>
													</Tooltip>
												</Styled>
											)}
											{hasBrowseAbandonmentEntitlement && (
												<Textarea
													appearance="inline"
													data-testid="browse-abandoned-custom-product-url-settings"
													label={t(
														'integrations:ecommerce:browse-abandoned-custom-product-url:label'
													)}
													placeholder={t(
														'integrations:ecommerce:browse-abandoned-custom-product-url:text-area-placeholder'
													)}
													required
													disabled={isFetching || settingsDisabled}
													rows={textareaRows}
													hasMinHeight={false}
													onChange={(e): void => onChangeCustomURL(e)}
													onFocus={(): void =>
														handleSelectSettingID({
															type: 'browse_abandonment.product_url_patterns',
														})
													}
													onKeyDown={(e): Promise<void> =>
														e.key === 'Enter' &&
														handleCustomUrlInputBlurOrEnter()
													}
													onBlur={(): Promise<void> =>
														handleCustomUrlInputBlurOrEnter()
													}
													invalid={!validCustomUrlSetting}
													helperText={urlPatternError || null}
													value={customUrlSetting}
													styles={{ width: '600px' }}
													fieldStyles={{
														resize: 'vertical',
														minHeight: `${textareaRows * 1.5}rem`,
														height: 'auto',
													}}
												/>
											)}
											<HelperText mt="sp200">
												{t(
													'integrations:ecommerce:browse-abandoned-custom-product-url:description'
												)}
											</HelperText>
											{(!integrationOptions?.connectionOptions?.length ||
												!hasValidUrlPattern) &&
												!isFetching && (
													<Text
														as="p"
														family="ffStandard"
														weight="fwMedium"
														height="lh200"
														size="fs200"
														mt="sp200"
														color="red"
													>
														{t(
															'integrations:ecommerce:browse-abandoned-custom-product-url:no-settings-enabled'
														)}
													</Text>
												)}
										</Styled>
									)}
							</>
						</Styled>
					</Accordion>
				</Styled>
			)}
			{(canSync(integration.service) || canManage(integration.service)) && (
				<Accordion title={t('integrations:detail-view:sync-header')}>
					{integration.service === INTEGRATION_TYPE_SHOPIFY && (
						<>
							{showSyncProgress && (
								<Styled p="sp400">
									<SyncProgress
										isVisible={showSyncProgress}
										integration={integration}
										entitlements={entitlements}
									/>
								</Styled>
							)}
							{!syncInProgress && (
								<>
									<Styled p="sp400">
										{!hasSyncEntitlement && (
											<Styled styles={{ display: 'inline-block' }}>
												<Tooltip
													placement="top"
													content={upsellTooltipContent()}
													contentHeavy
													contentHeavyAction={upsellTooltipBtn}
												>
													<Button.Outline
														data-testid="action-sync-button"
														disabled={true}
													>
														{t('integrations:detail-view:manual-sync-button')}
													</Button.Outline>
												</Tooltip>
											</Styled>
										)}
										{hasSyncEntitlement && (
											<Button.Outline
												data-testid="action-sync-button"
												disabled={
													(type === INTEGRATION_TYPE_FACEBOOK &&
														!fbookAudienceEnabled) ||
													isConnectionError(integration)
												}
												onClick={async (): Promise<void> =>
													await syncIntegrationMutate()
												}
											>
												{t('integrations:detail-view:manual-sync-button')}
											</Button.Outline>
										)}
										<HelperText mt="sp200">
											{type === INTEGRATION_TYPE_FACEBOOK &&
												t('integrations:facebook_audience:sync-explain')}
											{integration.lastSync &&
												type === INTEGRATION_TYPE_ECOMMERCE &&
												t('integrations:detail-view:manual-sync-explain', {
													syncDate: moment(
														formatDatetime(integration.lastSync)
													).format('dddd, MMMM Do YYYY (h:mma)'),
												})}
											{!integration.lastSync &&
												type === INTEGRATION_TYPE_ECOMMERCE &&
												t('integrations:detail-view:generic-sync-explain')}
										</HelperText>
									</Styled>
									{type === INTEGRATION_TYPE_ECOMMERCE && (
										<>
											<Styled p="sp400">
												{!hasSyncEntitlement && (
													<Styled styles={{ display: 'inline-block' }}>
														<Tooltip
															placement="top"
															content={upsellTooltipContent()}
															contentHeavy
															contentHeavyAction={upsellTooltipBtn}
														>
															<Button.Outline
																data-testid="action-sync-button"
																disabled={true}
															>
																{t('integrations:detail-view:sync-button')}
															</Button.Outline>
														</Tooltip>
													</Styled>
												)}
												{hasSyncEntitlement && (
													<Button.Outline
														data-testid="action-sync-button"
														disabled={isConnectionError(integration)}
														onClick={async (): Promise<void> =>
															await historicSyncIntegrationMutate()
														}
													>
														{t('integrations:detail-view:sync-button')}
													</Button.Outline>
												)}
												<HelperText mt="sp200">
													{t('integrations:detail-view:historic-sync-explain')}
												</HelperText>
											</Styled>
										</>
									)}
								</>
							)}
						</>
					)}
					{integration.service !== INTEGRATION_TYPE_SHOPIFY && (
						<>
							<Styled p="sp400">
								{!hasSyncEntitlement && (
									<Styled styles={{ display: 'inline-block' }}>
										<Tooltip
											placement="top"
											content={upsellTooltipContent()}
											contentHeavy
											contentHeavyAction={upsellTooltipBtn}
										>
											<Button.Outline
												data-testid="action-sync-button"
												disabled={true}
											>
												{syncInProgress ? (
													<>
														<Styled mr="sp300" as="span">
															<LoadingIndicator
																appearance="default"
																size="small"
															/>
														</Styled>
														{t('integrations:detail-view:sync-button-syncing')}
													</>
												) : (
													t('integrations:detail-view:manual-sync-button')
												)}
											</Button.Outline>
										</Tooltip>
									</Styled>
								)}
								{hasSyncEntitlement && (
									<>
										<Button.Outline
											data-testid="action-sync-button"
											disabled={
												syncInProgress ||
												(type === INTEGRATION_TYPE_FACEBOOK &&
													!fbookAudienceEnabled) ||
												isConnectionError(integration)
											}
											onClick={async (): Promise<void> =>
												await syncIntegrationMutate()
											}
										>
											{syncInProgress ? (
												<>
													<Styled mr="sp300" as="span">
														<LoadingIndicator
															appearance="default"
															size="small"
														/>
													</Styled>
													{t('integrations:detail-view:sync-button-syncing')}
												</>
											) : (
												t('integrations:detail-view:manual-sync-button')
											)}
										</Button.Outline>
									</>
								)}
								<HelperText mt="sp200">
									{type === INTEGRATION_TYPE_FACEBOOK &&
										t('integrations:facebook_audience:sync-explain')}
									{integration.lastSync &&
										type === INTEGRATION_TYPE_ECOMMERCE &&
										t('integrations:detail-view:manual-sync-explain', {
											syncDate: moment(
												formatDatetime(integration.lastSync)
											).format('dddd, MMMM Do YYYY (h:mma)'),
										})}
									{!integration.lastSync &&
										type === INTEGRATION_TYPE_ECOMMERCE &&
										t('integrations:detail-view:generic-sync-explain')}
								</HelperText>
							</Styled>
							{type === INTEGRATION_TYPE_ECOMMERCE && (
								<>
									<Styled p="sp400">
										{!hasSyncEntitlement && (
											<Styled styles={{ display: 'inline-block' }}>
												<Tooltip
													placement="top"
													content={upsellTooltipContent()}
													contentHeavy
													contentHeavyAction={upsellTooltipBtn}
												>
													<Button.Outline
														data-testid="action-sync-button"
														disabled={true}
													>
														{syncInProgress ? (
															<>
																<Styled mr="sp300" as="span">
																	<LoadingIndicator
																		appearance="default"
																		size="small"
																	/>
																</Styled>
																{t(
																	'integrations:detail-view:sync-button-syncing'
																)}
															</>
														) : (
															t('integrations:detail-view:sync-button')
														)}
													</Button.Outline>
												</Tooltip>
											</Styled>
										)}
										{hasSyncEntitlement && (
											<Button.Outline
												data-testid="action-sync-button"
												disabled={
													syncInProgress || isConnectionError(integration)
												}
												onClick={async (): Promise<void> =>
													await historicSyncIntegrationMutate()
												}
											>
												{syncInProgress ? (
													<>
														<Styled mr="sp300" as="span">
															<LoadingIndicator
																appearance="default"
																size="small"
															/>
														</Styled>
														{t('integrations:detail-view:sync-button-syncing')}
													</>
												) : (
													t('integrations:detail-view:sync-button')
												)}
											</Button.Outline>
										)}
										<HelperText mt="sp200">
											{t('integrations:detail-view:historic-sync-explain')}
										</HelperText>
									</Styled>
								</>
							)}
						</>
					)}
				</Accordion>
			)}
			{showInAppExpansionModal && (
				<InAppExpansion
					accountId={globalData?.acct?.account_id_old}
					intention={UpgradeIntentions.UPSELL_ECOM_DATA_SYNC}
					locale={globalData?.admin?.locale}
					handleDismiss={(): void => setShowInAppExpansionModal(false)}
					isAdmin={hasBillingAccess}
				/>
			)}
		</Styled>
	);
};
