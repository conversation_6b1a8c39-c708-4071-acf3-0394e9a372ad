import React, { useEffect, useState } from 'react';
import Modal from '@activecampaign/camp-components-modal';
import LoadingIndicator from '@activecampaign/camp-components-loading-indicator';
import Styled from '@activecampaign/camp-components-styled';
import Text from '@activecampaign/camp-components-text';
import Flex from '@activecampaign/camp-components-flex';
import Button from '@activecampaign/camp-components-button';
import styles from './app-integrations-checklist.styles';
import { useTranslation } from '@activecampaign/core-translations-client';
import { AUTOMATION_MODAL_TYPE } from '@src/constants';
import { importRecipe } from '@src/hooks';
import { createToast } from '@activecampaign/camp-components-toast';
import {
	useMutation,
	useQueryClient,
} from '@activecampaign/platform-core-queries';

type IntegrationsModalProps = {
	handleDismiss: Function;
	tplShareHost: string;
};

export const IntegrationsModal: React.FC<IntegrationsModalProps> = ({
	handleDismiss,
	tplShareHost,
}) => {
	const queryClient = useQueryClient();
	const [loading, setLoading] = useState<boolean>(true);
	const { t } = useTranslation();

	const {
		mutate: recipeImportMutate,
		data: recipeImportData,
		isLoading: recipeImportLoading,
		isSuccess: recipeImportSuccess,
		isError: recipeImportError,
	} = useMutation(() => importRecipe(tplShareHost));

	useEffect(() => {
		if (recipeImportLoading) {
			setLoading(true);
		} else {
			setLoading(false);
		}
		if (recipeImportError) {
			createToast({
				title: t('integrations:detail-view:toast-error'),
				appearance: 'danger',
				duration: 'standard',
				description: t('integrations:errors:unexpected_error:title'),
				dataTestId: 'integrations-sync-toast-error-api',
			});
		}
		if (recipeImportSuccess && recipeImportData?.data) {
			queryClient.invalidateQueries({
				queryKey: ['automationsData'],
			});
			console.info(recipeImportData?.data, 'recipeImportData');
			if (recipeImportData?.data?.succeeded !== 0) {
				window.location.href = `/series/${recipeImportData?.data?.id}`;
			} else {
				createToast({
					title: t('integrations:detail-view:toast-error'),
					appearance: 'danger',
					duration: 'standard',
					description: `${t('automations:recipe-modal:error')} ${
						recipeImportData?.data?.message
					}`,
					dataTestId: 'integrations-sync-toast-error-api',
				});
			}
		}
	}, [
		recipeImportData,
		recipeImportLoading,
		recipeImportSuccess,
		recipeImportError,
		queryClient,
		t,
	]);

	/**
	 * Resets modal to pristine state
	 * calls passed prop: handleDismiss
	 * sets modal title to null
	 * @returns {void}
	 */
	function resetModal(): void {
		handleDismiss();
	}

	return (
		<Modal
			onDismiss={(): void => resetModal()}
			data-testid="integrations-modal"
			title={t('onboarding:getting-started-ecom:automation-modal-title')}
			width={'850px'}
		>
			<Modal.Body>
				<Flex styles={styles.pageContent}>
					<img
						src="https://d226aj4ao1t61q.cloudfront.net/er6a9p70m_automation-preview.png"
						alt=""
					/>

					<Styled ml="sp900" as="div">
						<Styled mb="sp400" as="div">
							<Text
								as="h5"
								family="ffStandard"
								height="lh300"
								weight="fwMedium"
								size="fs300"
							>
								{t(
									'onboarding:getting-started-ecom:automation-modal-content:section-one-title'
								)}
							</Text>
							<Text
								mt="sp100"
								as="p"
								family="ffStandard"
								height="lh200"
								size="fs200"
							>
								{t(
									'onboarding:getting-started-ecom:automation-modal-content:section-one-content'
								)}
							</Text>
						</Styled>

						<Styled mb="sp400" as="div">
							<Text
								as="h5"
								family="ffStandard"
								height="lh300"
								weight="fwMedium"
								size="fs300"
							>
								{t(
									'onboarding:getting-started-ecom:automation-modal-content:section-two-title'
								)}
							</Text>
							<Text
								mt="sp100"
								as="p"
								family="ffStandard"
								height="lh200"
								size="fs200"
							>
								{t(
									'onboarding:getting-started-ecom:automation-modal-content:section-two-content'
								)}
							</Text>
						</Styled>

						<Styled as="div">
							<Text
								as="h5"
								family="ffStandard"
								height="lh300"
								weight="fwMedium"
								size="fs300"
							>
								{t(
									'onboarding:getting-started-ecom:automation-modal-content:section-three-title'
								)}
							</Text>
							<Text
								mt="sp100"
								as="p"
								family="ffStandard"
								height="lh200"
								size="fs200"
							>
								{t(
									'onboarding:getting-started-ecom:automation-modal-content:section-three-content'
								)}
							</Text>
						</Styled>
					</Styled>
				</Flex>
			</Modal.Body>
			<Modal.Footer dataTestId="integration-modal-footer">
				<Button.Outline
					mr="sp400"
					data-testid={`${AUTOMATION_MODAL_TYPE}-integration-cancel`}
					type="button"
					onClick={resetModal}
				>
					{t('global:cancel.translation')}
				</Button.Outline>
				<Button
					data-testid={`${AUTOMATION_MODAL_TYPE}-integration-action`}
					type="button"
					disabled={loading}
					onClick={async (): Promise<void> => await recipeImportMutate()}
				>
					{loading ? (
						<>
							<LoadingIndicator appearance="inverse" size="small" />
							<Styled ml="sp300" as="span">
								{t(
									'onboarding:getting-started-ecom:automation-modal-button-loading'
								)}
							</Styled>
						</>
					) : (
						t('onboarding:getting-started-ecom:automation-modal-button')
					)}
				</Button>
			</Modal.Footer>
		</Modal>
	);
};
