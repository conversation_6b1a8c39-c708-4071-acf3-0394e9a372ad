import React, { useEffect } from 'react';
import Modal from '@activecampaign/camp-components-modal';
import Styled from '@activecampaign/camp-components-styled';
import Text from '@activecampaign/camp-components-text';
import { useTranslation } from '@activecampaign/core-translations-client';
import styles from './delete-modal.styles';
import Button from '@activecampaign/camp-components-button';
import { deleteIntegration } from '@src/hooks';
import { Integration } from '@src/types';
import { createToast } from '@activecampaign/camp-components-toast';
import { useHistory } from 'react-router-dom';
import { navigate } from '@activecampaign/platform-core-history';
import {
	useMutation,
	useQueryClient,
} from '@activecampaign/platform-core-queries';

type DeleteModalProps = {
	integration: Integration | null;
	handleDismiss: Function;
};

export const DeleteModal: React.FC<DeleteModalProps> = ({
	handleDismiss,
	integration,
}) => {
	const { t } = useTranslation();
	const queryClient = useQueryClient();
	const history = useHistory();

	/**
	 * Resets modal to pristine state
	 * calls passed prop: handleDismiss
	 * sets modal title to null
	 * @returns {void}
	 */
	function resetModal(): void {
		handleDismiss();
	}

	const {
		mutate: deleteIntegrationMutation,
		isSuccess: deleteIntegrationSuccess,
		isError: deleteIntegrationError,
	} = useMutation(() => deleteIntegration(integration.id));

	useEffect(() => {
		if (deleteIntegrationSuccess) {
			queryClient.invalidateQueries({
				queryKey: ['integrationDetail'],
			});
			navigate('/app/integrations');
		}
		if (deleteIntegrationError) {
			createToast({
				title: t('integrations:detail-view:toast-error'),
				appearance: 'danger',
				duration: 'short',
				dataTestId: 'integrations-option-toast-error-delete',
			});
			queryClient.invalidateQueries({
				queryKey: ['integrationDetail'],
			});
			navigate('/app/integrations');
		}
	}, [
		deleteIntegrationSuccess,
		deleteIntegrationError,
		queryClient,
		history,
		t,
	]);

	const onDisconnect = async (): Promise<void> => {
		if (integration) {
			await deleteIntegrationMutation();
		}
	};

	return (
		<Modal
			onDismiss={(): void => resetModal()}
			data-testid="lists-modal"
			title={t('integrations:detail-view:modal:delete-integration')}
			width={'587px'}
		>
			<Styled as="div" data-testid="delete-integration-modal">
				<Modal.Body>
					<Text mb="sp400" size="fs200" family="ffStandard" height="lh200">
						{t('integrations:detail-view:modal:delete-integration-disclaimer')}
					</Text>
				</Modal.Body>
				<Modal.Footer dataTestId="integration-modal-footer">
					<Button.Outline
						data-testid={'delete-integration-cancel'}
						mr="sp400"
						onClick={(): void => resetModal()}
					>
						{t('global:cancel.translation')}
					</Button.Outline>
					<Button
						type="submit"
						styles={styles.minimumWidth}
						data-testid={'delete-integration-submit'}
						appearance={'destructive'}
						onClick={(): Promise<void> => onDisconnect()}
					>
						{t('integrations:detail-view:modal:delete-integration')}
					</Button>
				</Modal.Footer>
			</Styled>
		</Modal>
	);
};
