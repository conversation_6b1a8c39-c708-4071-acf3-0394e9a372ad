import React, { useContext, useState, useEffect } from 'react';
import { AppIntegrationsContext } from '@src/context';
import { useTranslation } from '@activecampaign/core-translations-client';
import Styled from '@activecampaign/camp-components-styled';
import LoadingIndicator from '@activecampaign/camp-components-loading-indicator';
import Flex from '@activecampaign/camp-components-flex';
import Text from '@activecampaign/camp-components-text';
import Card from '@activecampaign/camp-components-card';
import styles from './app-integrations-checklist.styles';
import {
	ChecklistCard,
	ChecklistCardItem,
	SyncProgress,
	IntegrationsModal,
} from '@src/components';
// import Button from '@activecampaign/camp-components-button';
import {
	// useOnboarding,
	useAutomations,
	useConnectedApps,
	syncIntegration,
} from '@src/hooks';
import {
	Integration,
	AppIntegrationsChecklistProps,
	Entitlements,
} from '@src/types';
import illustrations from '@activecampaign/camp-tokens-illustration';
import { SpotIllustration } from '@activecampaign/camp-components-illustration';
import { createToast } from '@activecampaign/camp-components-toast';
import {
	useMutation,
	useQueryClient,
	useEntitlements,
} from '@activecampaign/platform-core-queries';
import {
	INTEGRATION_TYPE_SHOPIFY,
	INTEGRATION_TYPE_WOO,
	GETTING_STARTED_CHECKLIST_TOTAL,
} from '@src/constants';
import {
	isSyncIsComplete,
	hasEntitlement,
	isSyncInProgress,
	checklistProgress,
} from '@src/utils';

export const AppIntegrationsChecklist: React.FC<
	AppIntegrationsChecklistProps
> = ({ data }) => {
	// console.info(data, 'data');
	let completedItems = 0;
	let firstStoreName = '';
	let totalItems = GETTING_STARTED_CHECKLIST_TOTAL;
	const queryClient = useQueryClient();
	const { t } = useTranslation();
	const { isLoading, globalData } = useContext(AppIntegrationsContext);
	const [loading, setLoading] = useState<boolean>(true);
	const [showModal, setShowModal] = useState<boolean>(false);
	const [syncInProgress, setSyncInProgress] = useState<boolean>(false);
	const [syncCompleted, setSyncCompleted] = useState<boolean>(false);
	const [hasConnectedEcomIntegration, setHasConnectedEcomIntegration] =
		useState<boolean>(false);
	const [hasAutomations, setHasAutomations] = useState<boolean>(false);
	const [connectedEcomIntegrations, setConnectedEcomIntegrations] = useState<
		Array<Integration>
	>([]);
	const [ecommerceEntitlements, setHasEcommerceEntitlements] =
		useState<Entitlements>(null);
	// const {
	// 	data: onboardingData,
	// 	isFetched: onboardingFetched,
	// 	isLoading: onboardingLoading,
	// } = useOnboarding();
	const {
		data: automationsData,
		isFetched: automationsFetched,
		isLoading: automationsLoading,
	} = useAutomations();
	const {
		data: connectedAppsData,
		isFetched: connectedAppsFetched,
		isLoading: connectedAppsLoading,
	} = useConnectedApps();
	const {
		data: entitlementsData,
		isFetched: fetchedEntitlements,
		isError: isErrorEntitlements,
		isLoading: entitlementsLoading,
	} = useEntitlements();

	useEffect(() => {
		if (
			isLoading ||
			// onboardingLoading ||
			connectedAppsLoading ||
			automationsLoading ||
			entitlementsLoading
		) {
			setLoading(true);
		} else {
			setLoading(false);
		}
		if (
			automationsData &&
			automationsFetched &&
			connectedAppsData &&
			connectedAppsFetched &&
			// onboardingData &&
			// onboardingFetched &&
			globalData
		) {
			// console.info(automationsData, 'automationsData');
			// console.info(onboardingData, 'onboardingData');
			// console.info(connectedAppsData, 'connectedAppsData');
			// console.info(globalData, 'globalData');
			if (connectedAppsData?.connections?.length > 0) {
				setConnectedEcomIntegrations(connectedAppsData?.connections);
				setHasConnectedEcomIntegration(true);
				setSyncInProgress(isSyncInProgress(connectedAppsData?.connections[0]));
				setSyncCompleted(isSyncIsComplete(connectedAppsData?.connections[0]));
			} else {
				setConnectedEcomIntegrations([]);
				setHasConnectedEcomIntegration(false);
				setSyncInProgress(false);
			}
			if (automationsData?.automations?.length > 0) {
				setHasAutomations(true);
			} else {
				setHasAutomations(false);
			}
		}
		if (fetchedEntitlements && !ecommerceEntitlements) {
			setHasEcommerceEntitlements({
				fetchedEntitlements,
				isErrorEntitlements,
				entitlementsData,
			});
		}
	}, [
		automationsData,
		automationsFetched,
		automationsLoading,
		connectedAppsData,
		connectedAppsFetched,
		connectedAppsLoading,
		ecommerceEntitlements,
		entitlementsData,
		entitlementsLoading,
		fetchedEntitlements,
		globalData,
		isErrorEntitlements,
		isLoading,
		// onboardingData,
		// onboardingFetched,
		// onboardingLoading,
	]);

	const hasSyncEntitlement =
		hasEntitlement(
			ecommerceEntitlements?.fetchedEntitlements,
			ecommerceEntitlements?.isErrorEntitlements,
			ecommerceEntitlements?.entitlementsData,
			['ecom-historical-sync']
		) ||
		hasEntitlement(
			ecommerceEntitlements?.fetchedEntitlements,
			ecommerceEntitlements?.isErrorEntitlements,
			ecommerceEntitlements?.entitlementsData,
			['ecommerce']
		);

	if (hasSyncEntitlement) {
		totalItems++;
	}

	const {
		mutate: syncIntegrationMutate,
		isLoading: syncIntegrationLoading,
		isSuccess: syncIntegrationSuccess,
		isError: syncIntegrationError,
	} = useMutation((integration: Integration) =>
		syncIntegration(integration?.id)
	);

	useEffect(() => {
		if (syncIntegrationSuccess) {
			queryClient.invalidateQueries({
				queryKey: ['connectedApps'],
			});
		}
		if (syncIntegrationError) {
			createToast({
				title: t('integrations:detail-view:toast-error'),
				appearance: 'danger',
				duration: 'standard',
				description: t('integrations:errors:unexpected_error:title'),
				dataTestId: 'integrations-sync-toast-error-api',
			});
		}
	}, [
		syncIntegrationLoading,
		syncIntegrationSuccess,
		syncIntegrationError,
		queryClient,
		t,
	]);

	if (hasConnectedEcomIntegration) {
		completedItems++;
		firstStoreName = connectedAppsData?.connections[0]?.externalid;
	}
	if (syncCompleted) {
		completedItems++;
	}
	if (hasAutomations) {
		completedItems++;
	}

	return (
		<>
			<Styled
				data-testid="page-content"
				styles={styles.pageContent}
				className="ecomm-getting-started-AppIntegrationsChecklist"
			>
				{loading && (
					<Flex
						alignItems="center"
						justifyContent="center"
						px="sp500"
						py="sp1200"
					>
						<LoadingIndicator appearance="default" size="medium" />
					</Flex>
				)}
				{!loading && (
					<Styled
						data-testid="app-integrations-checklist"
						p="sp500"
						styles={styles.pageContentInner}
					>
						<Card p="sp500">
							<Text
								as="h2"
								family="ffStandard"
								height="lh300"
								size="fs300"
								weight="fwSemiBold"
								color="slate600"
							>
								{t('onboarding:getting-started:overall-progress', {
									dividend: completedItems,
									divisor: totalItems,
								})}
							</Text>
							<Styled
								as="div"
								mt="sp400"
								data-testid="checklist-progress"
								styles={styles.checklistProgress}
							>
								<Styled
									as="div"
									styles={styles.checklistProgressBar(
										`${checklistProgress(completedItems, totalItems)}%`
									)}
								>
									&nbsp;
								</Styled>
							</Styled>
						</Card>
						<ChecklistCard
							progress={hasConnectedEcomIntegration ? 100 : 0}
							defaultExpand={!hasConnectedEcomIntegration}
							canExpand={true}
							header={t('onboarding:getting-started-ecom:chapter-one-title')}
							subheader={t(
								'onboarding:getting-started-ecom:chaper-one-subtitle'
							)}
						>
							<>
								{hasConnectedEcomIntegration && (
									<ChecklistCardItem
										header={`${connectedEcomIntegrations[0].externalid} (${
											connectedEcomIntegrations[0].service
										}) ${t(
											'onboarding:getting-started-ecom:store-is-connected'
										)}`}
										completed={hasConnectedEcomIntegration}
										showButton={false}
										showSecondary={false}
									/>
								)}
								{!hasConnectedEcomIntegration && (
									<ChecklistCardItem
										header={t(
											'onboarding:getting-started-ecom:chaper-one-item-one-not-connected'
										)}
										completed={false}
										handleClick={(): void => {
											data?.connectToService();
										}}
										buttonText={t(
											'onboarding:getting-started-ecom:chapter-one-item-one-button-text'
										)}
										showButton={true}
										showSecondary={false}
									/>
								)}
							</>
						</ChecklistCard>
						{hasSyncEntitlement && (
							<ChecklistCard
								progress={
									syncCompleted ||
									connectedAppsData?.connections[0]?.service ===
										INTEGRATION_TYPE_WOO
										? 100
										: 0
								}
								canExpand={hasConnectedEcomIntegration}
								defaultExpand={hasConnectedEcomIntegration && !syncCompleted}
								header={t(
									'onboarding:getting-started-ecom:sync-section-header'
								)}
								subheader={t(
									'onboarding:getting-started-ecom:sync-section-subheader'
								)}
							>
								<>
									{connectedAppsData?.connections[0]?.service ===
										INTEGRATION_TYPE_WOO && (
										<ChecklistCardItem
											buttonText={t(
												'onboarding:getting-started-ecom:chapter-one-item-two-button-text:woocommerce'
											)}
											header={t(
												'onboarding:getting-started-ecom:chaper-one-item-two-title:woocommerce'
											)}
											subheader={t(
												'onboarding:getting-started-ecom:chaper-one-item-two-subtitle:woocommerce',
												{
													pluginInstallLink:
														firstStoreName.charAt(firstStoreName.length - 1) ===
														'/'
															? `${firstStoreName}wp-admin/plugin-install.php?s=ActiveCampaign&tab=search&type=term`
															: `${firstStoreName}/wp-admin/plugin-install.php?s=ActiveCampaign&tab=search&type=term`,
												}
											)}
											completed={syncCompleted}
											showButton={true}
											showSecondary={false}
											handleClick={(): Window => {
												if (
													firstStoreName.charAt(firstStoreName.length - 1) ===
													'/'
												) {
													return window.open(
														`${firstStoreName}wp-admin/admin.php?page=activecampaign_for_woocommerce_historical_sync`,
														'_blank'
													);
												} else {
													return window.open(
														`${firstStoreName}/wp-admin/admin.php?page=activecampaign_for_woocommerce_historical_sync`,
														'_blank'
													);
												}
											}}
										/>
									)}
									{connectedAppsData?.connections[0]?.service !==
										INTEGRATION_TYPE_WOO && (
										<>
											{syncCompleted && (
												<ChecklistCardItem
													buttonText={t('integrations:detail-view:sync-header')}
													header={t(
														'onboarding:getting-started-ecom:sync-section:item-one-header'
													)}
													subheader={t(
														'onboarding:getting-started-ecom:sync-section:item-one-subheader'
													)}
													completed={true}
													showButton={false}
													showSecondary={false}
												/>
											)}
											{!syncCompleted && syncInProgress && (
												<ChecklistCardItem
													buttonText={t('integrations:detail-view:sync-header')}
													header={t(
														'onboarding:getting-started-ecom:sync-section:item-one-header'
													)}
													subheader={t(
														'onboarding:getting-started-ecom:sync-section:item-one-subheader'
													)}
													completed={false}
													showButton={true}
													showSecondary={false}
													disableButton={true}
													handleClick={async (): Promise<void> =>
														await syncIntegrationMutate(
															connectedEcomIntegrations[0]
														)
													}
												>
													<>
														{connectedEcomIntegrations[0].service ===
															INTEGRATION_TYPE_SHOPIFY && (
															<Styled mt="sp400">
																<SyncProgress
																	isVisible={true}
																	integration={connectedEcomIntegrations[0]}
																	entitlements={ecommerceEntitlements}
																/>
															</Styled>
														)}
														{connectedEcomIntegrations[0].service !==
															INTEGRATION_TYPE_SHOPIFY && (
															<Styled mt="sp400" as="div">
																<Styled mr="sp300" as="span">
																	<LoadingIndicator
																		appearance="default"
																		size="small"
																	/>
																</Styled>
																<Text
																	as="span"
																	family="ffStandard"
																	height="lh200"
																	size="fs200"
																>
																	{t(
																		'integrations:detail-view:sync-button-syncing'
																	)}
																</Text>
															</Styled>
														)}
													</>
												</ChecklistCardItem>
											)}
											{!syncCompleted && !syncInProgress && (
												<ChecklistCardItem
													buttonText={t('integrations:detail-view:sync-header')}
													header={t(
														'onboarding:getting-started-ecom:sync-section:item-one-header'
													)}
													subheader={t(
														'onboarding:getting-started-ecom:sync-section:item-one-subheader'
													)}
													completed={false}
													showButton={true}
													showSecondary={false}
													disableButton={!hasConnectedEcomIntegration}
													handleClick={async (): Promise<void> =>
														await syncIntegrationMutate(
															connectedEcomIntegrations[0]
														)
													}
												/>
											)}
										</>
									)}
								</>
							</ChecklistCard>
						)}
						<ChecklistCard
							progress={hasAutomations ? 100 : 0}
							canExpand={
								hasConnectedEcomIntegration &&
								(!hasSyncEntitlement ||
									(hasSyncEntitlement && syncCompleted) ||
									connectedAppsData?.connections[0]?.service ===
										INTEGRATION_TYPE_WOO)
							}
							defaultExpand={
								hasConnectedEcomIntegration &&
								(!hasSyncEntitlement ||
									(hasSyncEntitlement && syncCompleted) ||
									connectedAppsData?.connections[0]?.service ===
										INTEGRATION_TYPE_WOO)
							}
							header={t(
								'onboarding:getting-started-ecom:automation-chapter-title'
							)}
							subheader={t(
								'onboarding:getting-started-ecom:automation-chapter-subtitle'
							)}
						>
							<>
								<ChecklistCardItem
									handleClick={
										hasAutomations
											? (): Window => window.open('/app/automations', '_blank')
											: (): void => setShowModal(true)
									}
									buttonText={
										hasAutomations
											? t(
													'onboarding:getting-started-ecom:automation-chapter-item-one-button'
											  )
											: t('automations:get-started')
									}
									header={t(
										'onboarding:getting-started-ecom:automation-chapter-item-one-title'
									)}
									subheader={t(
										'onboarding:getting-started-ecom:automation-chapter-item-one-subtitle'
									)}
									completed={hasAutomations}
									disableButton={!hasConnectedEcomIntegration}
									secondaryButtonText={t('automations:get-started')}
									showButton={true}
									showSecondary={hasAutomations}
									handleSecondaryClick={(): void => setShowModal(true)}
								/>
							</>
						</ChecklistCard>
						<Flex
							mt="sp500"
							justifyContent="space-between"
							wrap="nowrap"
							styles={styles.educationCards}
							className="ecomm-getting-started-educationCards"
						>
							<Card p="sp500" styles={styles.educationCard}>
								<Flex alignItems="center" wrap="nowrap">
									<Styled mr="sp600">
										<SpotIllustration
											size="small"
											use={illustrations.dataGraphReportSpot}
											decorative={true}
										/>
									</Styled>
									<Text
										as="h5"
										family="ffStandard"
										height="lh300"
										weight="fwMedium"
										size="fs300"
									>
										{t(
											'onboarding:getting-started-ecom:recommendation-card-dashboard-title'
										)}
									</Text>
								</Flex>
								<Text
									mt="sp300"
									as="p"
									family="ffStandard"
									height="lh200"
									size="fs200"
								>
									<Styled
										as="span"
										data-testid="approval-declined-with-ticket"
										dangerouslySetInnerHTML={{
											__html: t(
												'onboarding:getting-started-ecom:recommendation-card-dashboard-subtitle',
												{
													dashboardLink: '/overview/ecommerce/',
												}
											),
										}}
									/>
								</Text>
							</Card>
							<Card p="sp500" styles={styles.educationCard}>
								<Flex alignItems="center" wrap="nowrap">
									<Styled mr="sp600">
										<SpotIllustration
											size="small"
											use={illustrations.automationSpot}
											decorative={true}
										/>
									</Styled>
									<Text
										as="h5"
										family="ffStandard"
										height="lh300"
										weight="fwMedium"
										size="fs300"
									>
										{t(
											'onboarding:getting-started-ecom:recommendation-card-recipes-title'
										)}
									</Text>
								</Flex>
								<Text
									mt="sp300"
									as="p"
									family="ffStandard"
									height="lh200"
									size="fs200"
								>
									<Styled
										as="span"
										data-testid="approval-declined-with-ticket"
										dangerouslySetInnerHTML={{
											__html: t(
												'onboarding:getting-started-ecom:recommendation-card-recipes-subtitle',
												{
													automationRecipesLink:
														'https://www.activecampaign.com/marketplace/?s=date&c=37&t=1&xp=3%2C2',
												}
											),
										}}
									/>
								</Text>
							</Card>
							<Card p="sp500" styles={styles.educationCard}>
								<Flex alignItems="center" wrap="nowrap">
									<Styled mr="sp600">
										<SpotIllustration
											size="small"
											use={illustrations.graduationSpot}
											decorative={true}
										/>
									</Styled>
									<Text
										as="h5"
										family="ffStandard"
										height="lh300"
										weight="fwMedium"
										size="fs300"
									>
										{t(
											'onboarding:getting-started-ecom:recommendation-card-onboarding-title'
										)}
									</Text>
								</Flex>
								<Text
									mt="sp300"
									as="p"
									family="ffStandard"
									height="lh200"
									size="fs200"
								>
									<Styled
										as="span"
										data-testid="approval-declined-with-ticket"
										dangerouslySetInnerHTML={{
											__html: t(
												'onboarding:getting-started-ecom:recommendation-card-onboarding-subtitle',
												{
													ecomAcceleratedOnboardingLink:
														'https://www.activecampaign.com/resources/accelerated-onboarding#upcoming-events',
												}
											),
										}}
									/>
								</Text>
							</Card>
						</Flex>
					</Styled>
				)}
				{showModal && (
					<IntegrationsModal
						handleDismiss={(): void => setShowModal(false)}
						tplShareHost={globalData.tplShareHost}
					/>
				)}
			</Styled>
		</>
	);
};
