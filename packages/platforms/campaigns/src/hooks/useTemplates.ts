import {
	UseMutationResult,
	UseQ<PERSON>yOptions,
	UseQueryResult,
	useMutation,
	useQuery,
} from '@tanstack/react-query';
import { createTemplate, getTemplate } from '@src/api/templates';

import { AxiosResponse } from 'axios';

export const useCreateTemplate = (): UseMutationResult<AxiosResponse> => {
	return useMutation((requestBody: CreateTemplateRequest) =>
		createTemplate(requestBody)
	);
};

export const useGetTemplate = (
	id: number | string,
	options?: UseQueryOptions<Template>
): UseQueryResult<Template> => {
	return useQuery({
		queryKey: ['template', id],
		queryFn: async () => {
			const {
				data: { template },
			} = await getTemplate(id);
			return template as Template;
		},
		...options,
	});
};
