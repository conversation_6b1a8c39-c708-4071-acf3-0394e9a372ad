import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { redirectForAuth } from '@src/utils/Authentication';

import { getCampaignsExist } from '../api/campaigns';

export const useCampaignsExistQuery = (
	setHasError: Function,
	setIsAuthenticating: Function
): UseQueryResult<{
	hasCampaigns: boolean;
}> => {
	const result = useQuery({
		queryKey: 'hasCampaigns',
		queryFn: async () => {
			const { data } = await getCampaignsExist();

			return data;
		},
		onError: (error) => {
			//  @ts-ignore
			if (error.response && error.response.status === 401) {
				redirectForAuth();
				setIsAuthenticating(true);
				return;
			}

			setHasError(true);
		},
	});

	return result;
};
