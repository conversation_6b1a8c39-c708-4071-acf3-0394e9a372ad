import '@testing-library/jest-dom';

import * as ReactRouterDom from 'react-router-dom';

import { fireEvent, render, screen } from '@testing-library/react';

import React from 'react';
import { Router } from 'react-router-dom';
import { SaveAndExit } from '../SaveAndExit';
import { redirectToAutomation } from '@src/utils/Navigation';
import { useCampaignId } from '@src/hooks/useCampaignId';
import { useCampaignSummary } from '@src/hooks/useCampaignSummary';
import { useIsAutomationCampaign } from '@src/hooks/useAutomations';
import { useIsMutating } from '@tanstack/react-query';
import { useTranslation } from '@activecampaign/core-translations-client';

jest.mock('@src/hooks/useCampaignId');
jest.mock('@src/hooks/useCampaignSummary');
jest.mock('@src/hooks/useAutomations');
jest.mock('@activecampaign/core-translations-client');
jest.mock('@src/utils/Navigation');
jest.mock('@tanstack/react-query');

jest.mock('react-router-dom', () => ({
	...(jest.requireActual('react-router-dom') as Record<string, unknown>),
	useHistory: jest.fn().mockReturnValue({
		push: jest.fn(),
		listen: jest.fn(),
		location: {
			pathname: '/app/campaigns',
		},
	}),
}));

describe('SaveAndExit', () => {
	const history = ReactRouterDom.useHistory();

	beforeEach(() => {
		(useCampaignId as jest.Mock).mockReturnValue('1');
		(useTranslation as jest.Mock).mockReturnValue({
			t: jest.fn().mockReturnValue('translated text'),
		});
		(useIsMutating as jest.Mock).mockReturnValue(0);
		(useCampaignSummary as jest.Mock).mockReturnValue({
			data: { scheduledDate: null },
		});
		(useIsAutomationCampaign as jest.Mock).mockReturnValue(false);
	});

	it('renders without crashing', () => {
		render(
			<Router history={history}>
				<SaveAndExit />
			</Router>
		);
		expect(
			screen.getByTestId('campaign-flow-type-tertiary-btn')
		).toBeInTheDocument();
	});

	it('renders Button.Text with correct props when campaign.scheduledDate is null', () => {
		render(
			<Router history={history}>
				<SaveAndExit />
			</Router>
		);
		expect(screen.getByText('translated text')).toBeInTheDocument();
	});

	it('handleOnClick redirects to the correct path when isAutomated is false', () => {
		render(
			<Router history={history}>
				<SaveAndExit />
			</Router>
		);
		fireEvent.click(screen.getByTestId('campaign-flow-type-tertiary-btn'));
		expect(history.location.pathname).toBe('/app/campaigns');
	});

	it('handleOnClick redirects to the correct path when isAutomated is true', () => {
		(useIsAutomationCampaign as jest.Mock).mockReturnValueOnce(true);
		render(
			<Router history={history}>
				<SaveAndExit />
			</Router>
		);
		fireEvent.click(screen.getByTestId('campaign-flow-type-tertiary-btn'));
		expect(redirectToAutomation).toHaveBeenCalled();
	});
});
